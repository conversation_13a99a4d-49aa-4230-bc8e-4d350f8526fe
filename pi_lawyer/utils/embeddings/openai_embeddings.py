"""
OpenAI embeddings utility for PI Lawyer AI.

This module provides utilities for generating text embeddings using
OpenAI's text embedding models. These embeddings are used for
semantic search and retrieval.
"""

import logging
import os
import time
from typing import Any, Dict, List, Optional

import backoff
import numpy as np
import tiktoken
from openai import OpenAI
from tenacity import (
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_random_exponential,
)

from ..structured_logging import log_with_context

# Set up logging
logger = logging.getLogger(__name__)

# Default model for embeddings
DEFAULT_EMBEDDING_MODEL = "text-embedding-3-small"


class EmbeddingConfig:
    """Configuration for embedding generation."""

    model: str = DEFAULT_EMBEDDING_MODEL
    dimensions: Optional[int] = None  # Use model default if not specified
    batch_size: int = 100  # For batch processing
    timeout: int = 60  # Seconds
    max_tokens_per_batch: int = 8000  # Token limit per batch
    encoding_name: str = "cl100k_base"  # Encoding for token counting


@retry(
    retry=retry_if_exception_type((TimeoutError, ConnectionError)),
    wait=wait_random_exponential(min=1, max=60),
    stop=stop_after_attempt(5),
)
def get_embedding_for_text(
    text: str, model: str = DEFAULT_EMBEDDING_MODEL, dimensions: Optional[int] = None
) -> List[float]:
    """
    Generate embedding for a single text using OpenAI.

    Args:
        text: The text to generate an embedding for
        model: The embedding model to use
        dimensions: Optional dimensionality of the embedding

    Returns:
        List[float]: The embedding vector

    Raises:
        ValueError: If the OpenAI API key is not set or text is empty
        Exception: If embedding generation fails
    """
    if not text or not text.strip():
        raise ValueError("Text cannot be empty")
    api_key = os.environ.get("OPENAI_API_KEY")
    if not api_key:
        raise ValueError("OpenAI API key not found in environment variables")
    client = OpenAI(api_key=api_key)

    try:
        # Log the embedding request (without the full text for privacy)
        text_preview = text[:50] + "..." if len(text) > 50 else text
        log_with_context(
            logger,
            "info",
            f"Generating embedding for text: {text_preview}",
            model=model,
        )

        # Create the embedding
        params: Dict[str, Any] = {
            "model": model,
            "input": text,
        }

        if dimensions:
            params["dimensions"] = dimensions

        response = client.embeddings.create(**params)

        # Extract the embedding from the response
        embedding = response.data[0].embedding

        return embedding

    except Exception as e:
        log_with_context(
            logger, "error", f"Error generating embedding: {str(e)}", model=model
        )
        raise


def get_embeddings_for_texts(
    texts: List[str], config: Optional[EmbeddingConfig] = None
) -> List[List[float]]:
    """
    Generate embeddings for multiple texts using OpenAI.
    Handles batching and rate limiting.

    Args:
        texts: List of texts to generate embeddings for
        config: Optional configuration for embedding generation

    Returns:
        List[List[float]]: List of embedding vectors

    Raises:
        ValueError: If the OpenAI API key is not set or texts is empty
        Exception: If embedding generation fails
    """
    if not texts:
        return []

    config = config or EmbeddingConfig()

    # Initialize tokenizer for counting tokens
    enc = tiktoken.get_encoding(config.encoding_name)

    # Calculate tokens for each text
    token_counts = [len(enc.encode(text)) for text in texts]

    # Batch texts to avoid token limits
    batches = []
    batch_start = 0
    current_batch_tokens = 0

    for i, token_count in enumerate(token_counts):
        if (
            current_batch_tokens + token_count > config.max_tokens_per_batch
            or i - batch_start >= config.batch_size
        ):
            # Start a new batch
            batches.append(texts[batch_start:i])
            batch_start = i
            current_batch_tokens = token_count
        else:
            # Add to current batch
            current_batch_tokens += token_count

    # Add the final batch
    if batch_start < len(texts):
        batches.append(texts[batch_start:])

    # Process batches
    all_embeddings = []
    for batch in batches:
        # Process each batch with retry mechanisms for API rate limits
        batch_embeddings = _process_embedding_batch(batch, config)
        all_embeddings.extend(batch_embeddings)

        # Sleep to avoid rate limits if there are more batches
        if len(batches) > 1:
            time.sleep(0.5)  # 500ms between batches

    return all_embeddings


@backoff.on_exception(
    backoff.expo, (TimeoutError, ConnectionError, Exception), max_tries=5, factor=2
)
def _process_embedding_batch(
    batch: List[str], config: EmbeddingConfig
) -> List[List[float]]:
    """
    Process a batch of texts for embedding generation.

    Args:
        batch: Batch of texts to embed
        config: Configuration for embedding generation

    Returns:
        List[List[float]]: List of embedding vectors for the batch

    Raises:
        Exception: If batch processing fails
    """
    api_key = os.environ.get("OPENAI_API_KEY")
    if not api_key:
        raise ValueError("OpenAI API key not found in environment variables")
    client = OpenAI(api_key=api_key)

    try:
        log_with_context(
            logger,
            "info",
            f"Generating embeddings for batch of {len(batch)} texts",
            model=config.model,
        )

        # Create the embeddings for the batch
        params: Dict[str, Any] = {
            "model": config.model,
            "input": batch,
        }

        if config.dimensions:
            params["dimensions"] = config.dimensions

        response = client.embeddings.create(**params)

        # Sort by index to maintain original order
        sorted_data = sorted(response.data, key=lambda x: x.index)

        # Extract embeddings
        embeddings = [item.embedding for item in sorted_data]

        return embeddings

    except Exception as e:
        log_with_context(
            logger,
            "error",
            f"Error generating batch embeddings: {str(e)}",
            model=config.model,
            batch_size=len(batch),
        )
        raise


def cosine_similarity(embedding1: List[float], embedding2: List[float]) -> float:
    """
    Calculate cosine similarity between two embeddings.

    Args:
        embedding1: First embedding vector
        embedding2: Second embedding vector

    Returns:
        float: Cosine similarity (0 to 1)

    Raises:
        ValueError: If embeddings have different dimensions
    """
    if len(embedding1) != len(embedding2):
        raise ValueError(
            f"Embeddings have different dimensions: {len(embedding1)} vs "
            f"{len(embedding2)}"
        )

    # Convert to numpy arrays for efficient computation
    vec1 = np.array(embedding1)
    vec2 = np.array(embedding2)

    # Calculate cosine similarity
    dot_product = np.dot(vec1, vec2)
    norm1 = np.linalg.norm(vec1)
    norm2 = np.linalg.norm(vec2)

    if norm1 == 0 or norm2 == 0:
        return 0.0

    similarity = dot_product / (norm1 * norm2)

    return float(similarity)
