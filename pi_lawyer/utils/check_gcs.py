"""
Check GCS bucket existence and configuration
"""

import json
import os

import google.cloud.storage
import google.oauth2.service_account
from dotenv import load_dotenv

# Aliases for better readability
storage = google.cloud.storage
service_account = google.oauth2.service_account

# Load environment variables
load_dotenv()

GCS_BUCKET_NAME = os.getenv("GCS_BUCKET_NAME")
GCS_SERVICE_ACCOUNT_FILE = os.getenv("GCS_SERVICE_ACCOUNT_FILE")


def main() -> None:
    """Check GCS configuration."""
    print("GCS Configuration Check")
    print("======================")

    print(f"Bucket name: {GCS_BUCKET_NAME}")
    print(f"Service account file: {GCS_SERVICE_ACCOUNT_FILE}")

    # Check if service account file exists and is readable
    if not GCS_SERVICE_ACCOUNT_FILE or not os.path.exists(GCS_SERVICE_ACCOUNT_FILE):
        print(
            f"ERROR: Service account file does not exist or is not defined: "
            f"{GCS_SERVICE_ACCOUNT_FILE}"
        )
        return

    try:
        # Check service account file content
        if not GCS_SERVICE_ACCOUNT_FILE:
            print("ERROR: Service account file path is not defined")
            return

        with open(GCS_SERVICE_ACCOUNT_FILE, "r") as f:
            service_account_info = json.load(f)
            print(f"Service account email: {service_account_info.get('client_email')}")
            print(f"Project ID: {service_account_info.get('project_id')}")

        print("\nAttempting to connect using service account...")
        credentials = service_account.Credentials.from_service_account_file(
            GCS_SERVICE_ACCOUNT_FILE
        )
        storage_client = storage.Client(
            credentials=credentials, project=service_account_info.get("project_id")
        )

        # Try to list buckets
        try:
            buckets = list(storage_client.list_buckets())
            print(f"Successfully connected to GCS! Found {len(buckets)} buckets.")
            for bucket in buckets:
                print(f" - {bucket.name}")

            # Check if our bucket exists
            if any(bucket.name == GCS_BUCKET_NAME for bucket in buckets):
                print(f"\nBucket '{GCS_BUCKET_NAME}' exists!")

                # Try to create a test object in the bucket
                bucket = storage_client.bucket(GCS_BUCKET_NAME)
                blob = bucket.blob("test/connectivity_test.txt")
                try:
                    blob.upload_from_string("This is a test of GCS connectivity.")
                    print(
                        "Successfully created test object: test/connectivity_test.txt"
                    )
                    # Clean up the test object
                    blob.delete()
                    print("Test object deleted.")
                except Exception as e:
                    print(f"ERROR: Could not create test object: {str(e)}")
            else:
                print(
                    f"\nERROR: Bucket '{GCS_BUCKET_NAME}' does not exist in this project. "
                    "Available buckets are listed above."
                )

        except Exception as e:
            print(f"ERROR: Failed to list buckets: {str(e)}")

    except Exception as e:
        print(f"ERROR: Failed to connect to GCS: {str(e)}")


if __name__ == "__main__":
    main()
