"""
Simple demonstration of GCS paths without accessing GCS.
"""

import sys
import uuid
from datetime import datetime
from typing import Optional


def generate_gcs_path(
    tenant_id: Optional[str] = None,
    case_id: Optional[str] = None,
    client_id: Optional[str] = None,
    practice_area: Optional[str] = None,
    document_category: str = "general",
    subcategory: Optional[str] = None,
    is_public: bool = False,
    jurisdiction: Optional[str] = None,
) -> str:
    """Generate appropriate GCS path based on document type and context."""
    date_prefix = datetime.utcnow().strftime("%Y/%m/%d")
    file_id = str(uuid.uuid4())

    if is_public:
        # Public reference documents
        if jurisdiction:
            return (
                f"public/jurisdictions/{jurisdiction}/{document_category}/"
                f"{subcategory or 'general'}/{file_id}"
            )
        elif practice_area:
            return (
                f"public/practice_areas/{practice_area}/{document_category}/"
                f"{subcategory or 'general'}/{file_id}"
            )
        return f"public/general/{document_category}/{file_id}"

    # Tenant-specific documents
    if not tenant_id:
        raise ValueError("Tenant ID is required for non-public documents")
    if case_id:
        # Case documents
        category_path = f"{document_category}/"
        if subcategory:
            category_path += f"{subcategory}/"

        practice_area_path = ""
        if practice_area and practice_area != "general":
            practice_area_path = f"{practice_area}/"

        return (
            f"tenants/{tenant_id}/cases/{case_id}/"
            f"{practice_area_path}{category_path}{date_prefix}/{file_id}"
        )

    elif client_id:
        # Client-specific but not case-specific documents
        return (
            f"tenants/{tenant_id}/clients/{client_id}/{document_category}/"
            f"{subcategory or 'general'}/{date_prefix}/{file_id}"
        )

    # Firm-wide documents
    return (
        f"tenants/{tenant_id}/case_documents/{document_category}/"
        f"{subcategory or 'general'}/{date_prefix}/{file_id}"
    )


def main() -> None:
    """Show example GCS paths."""
    print("GCS Path Structure Examples")
    print("==========================")

    # Get tenant_id and auth_user_id from command line if provided
    tenant_id = (
        sys.argv[1] if len(sys.argv) > 1 else "f5a731c2-9c0b-4ef8-bb6d-6bb9bd380a11"
    )

    print(f"Using tenant_id: {tenant_id}")
    print("\nExample paths that would be created:")
    print("----------------------------------")

    # Example 1: Firm document
    firm_doc_path = generate_gcs_path(
        tenant_id=tenant_id, document_category="policies", subcategory="admin"
    )
    print(f"1. Firm document (policies/admin):\n   {firm_doc_path}_firm_policy.txt\n")

    # Example 2: Case document
    case_id = "test-case-123"
    case_doc_path = generate_gcs_path(
        tenant_id=tenant_id,
        case_id=case_id,
        practice_area="personal_injury",
        document_category="medical",
        subcategory="reports",
    )
    print(
        f"2. Case document (personal_injury/medical/reports):\n   {case_doc_path}_medical_report.pdf\n"
    )

    # Example 3: Client document
    client_id = "test-client-456"
    client_doc_path = generate_gcs_path(
        tenant_id=tenant_id, client_id=client_id, document_category="intake"
    )
    print(f"3. Client document (intake):\n   {client_doc_path}_client_intake.pdf\n")

    # Example 4: Public jurisdiction document
    public_juris_path = generate_gcs_path(
        is_public=True,
        jurisdiction="texas",
        document_category="laws",
        subcategory="statutes",
    )
    print(
        f"4. Public jurisdiction document (texas/laws/statutes):\n   {public_juris_path}_texas_statutes.pdf\n"
    )

    # Example 5: Public practice area document
    public_practice_path = generate_gcs_path(
        is_public=True,
        practice_area="personal_injury",
        document_category="references",
        subcategory="guidelines",
    )
    print(
        f"5. Public practice area document (personal_injury/references/guidelines):\n"
        f"   {public_practice_path}_injury_guidelines.pdf\n"
    )

    print("These paths demonstrate the structure without actually accessing GCS.")
    print(
        "You can adapt these patterns when implementing document uploads in your app."
    )


if __name__ == "__main__":
    main()
