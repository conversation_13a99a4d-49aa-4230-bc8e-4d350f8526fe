"""
Gemini model client for legal research agent.

This module provides functions for interacting with Google's Gemini models,
specifically for generating legal research queries and synthesizing research.
"""

import logging
import os
from typing import Any, Dict, List

import google.generativeai as genai
from google.generativeai import GenerativeModel
from langchain_core.documents import Document

logger = logging.getLogger(__name__)


class GeminiClient:
    """Client for interacting with Google's Gemini models."""

    def __init__(self, model_name: str = "gemini-2.5-pro-exp-03-25"):
        """Initialize the Gemini client.

        Args:
            model_name: Name of the Gemini model to use
        """
        self.api_key = os.getenv("GEMINI_API_KEY")
        if not self.api_key:
            raise ValueError("GEMINI_API_KEY environment variable is required")
        genai.configure(api_key=self.api_key)
        self.model = GenerativeModel(model_name)
        logger.info(f"Initialized Gemini client with model {model_name}")

    async def generate_legal_queries(
        self, question: str, num_queries: int = 5
    ) -> List[str]:
        """Generate legal research queries based on a question.

        Args:
            question: The legal question to generate queries for
            num_queries: Number of queries to generate

        Returns:
            List of search queries
        """
        prompt = f"""You are an expert legal researcher specializing in personal injury law.
Generate {num_queries} search queries to thoroughly research this legal question:

QUESTION: {question}

Consider the following when generating queries:
1. Search for relevant case law, statutes, and precedents
2. Include jurisdiction-specific legal terminology
3. Focus on personal injury principles applicable to this case
4. Consider different legal theories that might apply

Generate {num_queries} diverse search queries that will help find comprehensive information.
Format each query on a new line preceded by a dash (-).
"""

        try:
            response = await self.model.generate_content_async(
                prompt, generation_config={"temperature": 0.2}
            )

            # Extract the queries from the response
            content = response.text
            queries = []
            for line in content.split("\n"):
                line = line.strip()
                if line.startswith("-"):
                    query = line[1:].strip()
                    if query:
                        queries.append(query)

            return queries[:num_queries]  # Limit to requested number

        except Exception as e:
            logger.error(f"Error generating legal queries: {e}")
            # Fallback to simple queries based on keywords from the question
            words = question.split()
            return [f"legal {' '.join(words[i:i+3])}" for i in range(0, len(words), 3)][
                :num_queries
            ]

    async def synthesize_research(
        self,
        question: str,
        documents: List[Document],
        confidence_threshold: float = 0.4,
    ) -> Dict[str, Any]:
        """Synthesize research from retrieved documents with strict guardrails.

        Args:
            question: The original legal question
            documents: List of retrieved documents with metadata
            confidence_threshold: Minimum confidence score for document inclusion

        Returns:
            Dictionary with synthesized research and metadata
        """
        # Filter documents by confidence score
        filtered_docs = []
        for doc in documents:
            score = doc.metadata.get("relevance_score", 0.0)
            if score >= confidence_threshold:
                filtered_docs.append(doc)

        # Check if we have any relevant documents after filtering
        if not filtered_docs:
            return {
                "content": "No relevant legal documents found for your question.",
                "has_relevant_docs": False,
                "doc_count": 0,
            }

        # Format documents for the prompt
        formatted_docs = "\n\n".join(
            [
                f"DOCUMENT {i+1} [Relevance: {doc.metadata.get('relevance_score', 'N/A')}]:\n{doc.page_content}"
                for i, doc in enumerate(filtered_docs)
            ]
        )

        prompt = f"""You are an expert legal researcher specializing in personal injury law.
Answer ONLY based on the specific legal documents provided below.

If the documents don't contain enough information to answer the query, say:
"Based on the legal documents available, I don't have enough information to fully answer this question."

Do NOT make up information, cite cases that aren't in the documents, or provide general legal advice that isn't supported by the documents.

QUESTION: {question}

LEGAL DOCUMENTS:
{formatted_docs}

Your response should:
1. Directly address the original question using ONLY the provided documents
2. Cite specific documents by their numbers when referencing information
3. Include a "Key Findings" section at the end summarizing the most important points
4. Note any limitations in the available information
"""

        try:
            response = await self.model.generate_content_async(
                prompt, generation_config={"temperature": 0.1}
            )

            return {
                "content": response.text,
                "has_relevant_docs": True,
                "doc_count": len(filtered_docs),
            }

        except Exception as e:
            logger.error(f"Error synthesizing research: {e}")
            error_msg = "I encountered an error while synthesizing the legal research. Here are some key points from the relevant documents:\n\n"

            # Extract main points from the top documents
            for i, doc in enumerate(filtered_docs[:3]):
                error_msg += f"- Document {i+1}: {doc.page_content[:200]}...\n"

            return {
                "content": error_msg,
                "has_relevant_docs": True,
                "doc_count": len(filtered_docs),
                "error": str(e),
            }
