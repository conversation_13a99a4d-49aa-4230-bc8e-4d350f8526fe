"""
User API endpoints for PI Lawyer AI.

This module provides FastAPI endpoints for user profile management,
demonstrating the use of authentication middleware.
"""

import logging
from typing import Any, Dict, List, Optional
from uuid import UUID

from fastapi import APIRouter, Body, Depends, HTTPException, Path, Query, status

from ..data import FirmRepository, UserProfileRepository
from ..models import UserProfile, UserProfileUpdate, UserRole
from ..utils.structured_logging import log_with_context
from .auth import get_current_user, get_optional_current_user, require_role

# Set up logging
logger = logging.getLogger(__name__)

# Initialize the router
router = APIRouter(prefix="/api", tags=["users"])


# Dependency injection for repositories
def get_user_profile_repository() -> UserProfileRepository:
    """Get a UserProfileRepository instance for dependency injection."""
    return UserProfileRepository(use_service_role=True)


def get_firm_repository() -> FirmRepository:
    """Get a FirmRepository instance for dependency injection."""
    return FirmRepository(use_service_role=True)


@router.get("/me", response_model=UserProfile)
async def get_current_user_profile(
    current_user: UserProfile = Depends(get_current_user),
) -> UserProfile:
    """
    Get the current authenticated user's profile.

    This endpoint requires user authentication.

    Args:
        current_user: The authenticated user

    Returns:
        UserProfile: The authenticated user's profile
    """
    return current_user


@router.put("/me", response_model=UserProfile)
async def update_current_user_profile(
    profile_update: UserProfileUpdate = Body(...),
    current_user: UserProfile = Depends(get_current_user),
    user_repo: UserProfileRepository = Depends(get_user_profile_repository),
) -> UserProfile:
    """
    Update the current authenticated user's profile.

    This endpoint requires user authentication.

    Args:
        profile_update: The profile updates
        current_user: The authenticated user
        user_repo: Repository for accessing user profiles

    Returns:
        UserProfile: The updated user profile

    Raises:
        HTTPException: If an error occurs
    """
    try:
        log_with_context(
            logger, "info", "Updating user profile", user_id=str(current_user.id)
        )

        # Prevent changing role or tenant_id through this endpoint
        if hasattr(profile_update, "role"):
            profile_update.role = None

        # Update the profile
        updated_profile = await user_repo.update_user_profile(
            current_user.id, profile_update
        )

        return updated_profile
    except Exception as e:
        logger.error(f"Error updating user profile: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update profile: {str(e)}",
        ) from e


@router.get("/firm/users", response_model=List[UserProfile])
async def get_firm_users(
    tenant_id: Optional[UUID] = Query(None, description="Tenant ID (optional)"),
    role: Optional[UserRole] = Query(None, description="Filter by role"),
    current_user: UserProfile = Depends(get_current_user),
    user_repo: UserProfileRepository = Depends(get_user_profile_repository),
) -> List[UserProfile]:
    """
    Get all users for the current user's firm/tenant.

    This endpoint requires user authentication.

    Args:
        tenant_id: Optional explicit tenant ID (if not provided, uses current user's)
        role: Optional role to filter by
        current_user: The authenticated user
        user_repo: Repository for accessing user profiles

    Returns:
        List[UserProfile]: The firm users

    Raises:
        HTTPException: If an error occurs or user is not authorized
    """
    try:
        # Determine which tenant to use
        effective_tenant_id = tenant_id or current_user.tenant_id

        # Security check - if user is not a partner, can only see their own tenant
        if (
            current_user.role != UserRole.PARTNER
            and effective_tenant_id != current_user.tenant_id
        ):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to access users from other firms",
            )
        log_with_context(
            logger,
            "info",
            "Getting firm users",
            tenant_id=str(effective_tenant_id),
            filter_role=role.value if role else None,
        )

        # Get users
        if role:
            users = await user_repo.get_users_by_role(effective_tenant_id, role)
        else:
            users = await user_repo.get_by_tenant_id(effective_tenant_id)

        return users
    except HTTPException:
        raise  # Re-raise HTTP exceptions
    except Exception as e:
        logger.error(f"Error getting firm users: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get firm users: {str(e)}",
        ) from e


@router.get("/admin/users/{user_id}", response_model=UserProfile)
async def admin_get_user(
    user_id: UUID = Path(..., description="User ID"),
    current_user: UserProfile = Depends(require_role([UserRole.PARTNER])),
    user_repo: UserProfileRepository = Depends(get_user_profile_repository),
) -> UserProfile:
    """
    Get a user profile by ID (admin/partner only).

    This endpoint requires partner role.

    Args:
        user_id: The user ID
        current_user: The authenticated user (must be a partner)
        user_repo: Repository for accessing user profiles

    Returns:
        UserProfile: The user profile

    Raises:
        HTTPException: If user not found or an error occurs
    """
    try:
        log_with_context(
            logger,
            "info",
            "Admin getting user profile",
            user_id=str(user_id),
            admin_id=str(current_user.id),
        )

        user = await user_repo.get_by_id(user_id)

        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
            )
        # Partners can only view users in their own firm
        if user.tenant_id != current_user.tenant_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to access this user",
            )
        return user
    except HTTPException:
        raise  # Re-raise HTTP exceptions
    except Exception as e:
        logger.error(f"Error getting user: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get user: {str(e)}",
        ) from e


@router.put("/admin/users/{user_id}/role", response_model=UserProfile)
async def admin_update_user_role(
    user_id: UUID = Path(..., description="User ID"),
    role: UserRole = Body(..., embed=True),
    current_user: UserProfile = Depends(require_role([UserRole.PARTNER])),
    user_repo: UserProfileRepository = Depends(get_user_profile_repository),
) -> UserProfile:
    """
    Update a user's role (admin/partner only).

    This endpoint requires partner role.

    Args:
        user_id: The user ID
        role: The new role
        current_user: The authenticated user (must be a partner)
        user_repo: Repository for accessing user profiles

    Returns:
        UserProfile: The updated user profile

    Raises:
        HTTPException: If user not found or an error occurs
    """
    try:
        log_with_context(
            logger,
            "info",
            f"Admin updating user role to {role}",
            user_id=str(user_id),
            admin_id=str(current_user.id),
        )

        # Get the user
        user = await user_repo.get_by_id(user_id)

        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
            )
        # Partners can only update users in their own firm
        if user.tenant_id != current_user.tenant_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to update this user",
            )
        # Update the role with all required fields for type checking
        profile_update = UserProfileUpdate(
            role=role,
            # Add all required fields with None values to satisfy type checking
            email=None,
            full_name=None,
            profile_image_url=None,
            phone=None,
            job_title=None,
            bio=None,
            preferences=None,
            is_active=None,
            metadata=None,
            tenant_id=None,
            updated_by=current_user.id,
        )
        updated_user = await user_repo.update_user_profile(user_id, profile_update)

        return updated_user
    except HTTPException:
        raise  # Re-raise HTTP exceptions
    except Exception as e:
        logger.error(f"Error updating user role: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update user role: {str(e)}",
        ) from e


@router.get("/public/firm-info/{tenant_id}")
async def get_public_firm_info(
    tenant_id: UUID = Path(..., description="Tenant/firm ID"),
    current_user: Optional[UserProfile] = Depends(get_optional_current_user),
    firm_repo: FirmRepository = Depends(get_firm_repository),
) -> Dict[str, Any]:
    """
    Get public information about a law firm.

    This endpoint supports optional authentication - it works whether
    the user is logged in or not.

    Args:
        tenant_id: The firm/tenant ID
        current_user: The current user if authenticated (optional)
        firm_repo: Repository for accessing firm data

    Returns:
        dict: Public firm information

    Raises:
        HTTPException: If firm not found or an error occurs
    """
    try:
        log_with_context(
            logger,
            "info",
            "Getting public firm info",
            tenant_id=str(tenant_id),
            authenticated=current_user is not None,
        )

        # Get the firm
        firm = await firm_repo.get_by_id(tenant_id)

        if not firm:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Firm not found"
            )
        if not firm.active:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Firm not found"
            )
        # Return only public information
        return {
            "id": str(firm.id),
            "name": firm.name,
            "description": firm.description,
            "primary_practice_area": firm.primary_practice_area,
            "primary_state": firm.primary_state,
            "website": firm.website,
        }
    except HTTPException:
        raise  # Re-raise HTTP exceptions
    except Exception as e:
        logger.error(f"Error getting public firm info: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get firm info: {str(e)}",
        ) from e
