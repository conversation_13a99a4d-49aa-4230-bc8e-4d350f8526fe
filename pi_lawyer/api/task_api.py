"""
Task API endpoints for PI Lawyer AI.

This module provides FastAPI endpoints for task management operations,
leveraging the type-safe task repository.
"""

import logging
import os
from datetime import datetime
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Body, Depends, HTTPException, Security
from fastapi.security import APIKeyHeader

from ..data import CaseRepository, TaskRepository, UserProfileRepository
from ..models import Task, TaskCreate, TaskPriority, TaskStatus, TaskUpdate
from ..utils.structured_logging import log_with_context

# Set up logging
logger = logging.getLogger(__name__)

# Initialize the router
router = APIRouter(prefix="/api", tags=["tasks"])

# Set up API key security
API_KEY_HEADER = APIKeyHeader(name="Authorization", auto_error=True)


async def validate_api_key(api_key: str = Security(API_KEY_HEADER)) -> bool:
    """
    Validate the API key from the Authorization header.

    Args:
        api_key: The API key from the Authorization header

    Returns:
        bool: Whether the API key is valid

    Raises:
        HTTPException: If the API key is invalid
    """
    expected_key = f"Bearer {os.environ.get('API_SERVICE_TOKEN')}"
    if api_key != expected_key:
        raise HTTPException(status_code=401, detail="Invalid API key")
    return True


# Dependency injection for repositories
def get_task_repository() -> TaskRepository:
    """Get a TaskRepository instance for dependency injection."""
    return TaskRepository(use_service_role=True)


def get_case_repository() -> CaseRepository:
    """Get a CaseRepository instance for dependency injection."""
    return CaseRepository(use_service_role=True)


def get_user_profile_repository() -> UserProfileRepository:
    """Get a UserProfileRepository instance for dependency injection."""
    return UserProfileRepository(use_service_role=True)


# Service layer that coordinates between repositories
class TaskService:
    """
    Service for coordinating task-related operations across repositories.

    This service encapsulates business logic that spans multiple repositories,
    demonstrating how to build service layers on top of repositories.
    """

    def __init__(
        self,
        task_repo: TaskRepository,
        case_repo: CaseRepository,
        user_repo: UserProfileRepository,
    ):
        self.task_repo = task_repo
        self.case_repo = case_repo
        self.user_repo = user_repo

    async def create_task_for_case(
        self, task_create: TaskCreate, case_id: UUID
    ) -> Task:
        """
        Create a task for a specific case with validation.

        Args:
            task_create: The task creation data
            case_id: The case ID

        Returns:
            Task: The created task

        Raises:
            ValueError: If case doesn't exist or other validation fails
        """
        # Validate case exists
        case = await self.case_repo.get_by_id(case_id)
        if not case:
            raise ValueError(f"Case with ID {case_id} not found")
        # Validate assigned user exists if provided
        if task_create.assigned_to:
            user = await self.user_repo.get_by_id(task_create.assigned_to)
            if not user:
                raise ValueError(f"User with ID {task_create.assigned_to} not found")
            # Validate user belongs to the tenant
            if user.tenant_id != task_create.tenant_id:
                raise ValueError("User does not belong to the tenant")
        # Ensure case_id is set
        task_create.case_id = case_id

        # Create task
        return await self.task_repo.create_task(task_create)

    async def reassign_task(self, task_id: UUID, user_id: UUID) -> Task:
        """
        Reassign a task to a different user.

        Args:
            task_id: The task ID
            user_id: The user ID to assign the task to

        Returns:
            Task: The updated task

        Raises:
            ValueError: If task or user doesn't exist
        """
        # Validate task exists
        task = await self.task_repo.get_by_id(task_id)
        if not task:
            raise ValueError(f"Task with ID {task_id} not found")
        # Validate user exists
        user = await self.user_repo.get_by_id(user_id)
        if not user:
            raise ValueError(f"User with ID {user_id} not found")
        # Validate user belongs to the tenant
        if user.tenant_id != task.tenant_id:
            raise ValueError("User does not belong to the tenant")
        # Create update object with all required fields
        task_update = TaskUpdate(
            assigned_to=user_id,
            updated_by=user_id,  # Fix: using updated_by instead of updated_at
            # Add all required fields with None value to satisfy type checking
            title=None,
            description=None,
            status=None,
            priority=None,
            due_date=None,
            completed_at=None,
            completed_by=None,
            case_id=None,
            metadata=None,
        )

        # Update task
        return await self.task_repo.update_task(task_id, task_update)

    async def get_overdue_tasks(self, tenant_id: UUID) -> List[Task]:
        """
        Get all overdue tasks for a tenant.

        Args:
            tenant_id: The tenant ID

        Returns:
            List[Task]: The overdue tasks
        """
        return await self.task_repo.get_overdue_tasks(tenant_id)


# Dependency injection for TaskService
def get_task_service(
    task_repo: TaskRepository = Depends(get_task_repository),
    case_repo: CaseRepository = Depends(get_case_repository),
    user_repo: UserProfileRepository = Depends(get_user_profile_repository),
) -> TaskService:
    """Get a TaskService instance for dependency injection."""
    return TaskService(task_repo, case_repo, user_repo)


@router.get("/tasks", response_model=List[Task])
async def get_tasks_for_tenant(
    tenant_id: UUID,
    status: Optional[TaskStatus] = None,
    priority: Optional[TaskPriority] = None,
    task_repo: TaskRepository = Depends(get_task_repository),
    _: bool = Depends(validate_api_key),
) -> List[Task]:
    """
    Get all tasks for a tenant with optional filters.

    Args:
        tenant_id: The tenant ID
        status: Optional filter by task status
        priority: Optional filter by task priority
        task_repo: Task repository for database access
        _: API key validation result (unused)

    Returns:
        List[Task]: The tasks

    Raises:
        HTTPException: If an error occurs
    """
    try:
        log_with_context(
            logger,
            "info",
            "Getting tasks for tenant",
            tenant_id=str(tenant_id),
            status=status.value if status else None,
            priority=priority.value if priority else None,
        )

        # Build filter
        filter_dict = {"tenant_id": str(tenant_id)}

        if status:
            filter_dict["status"] = status

        if priority:
            filter_dict["priority"] = priority

        # Get tasks using the typed repository
        tasks = await task_repo.get_by_filter(filter_dict)
        return tasks

    except Exception as e:
        logger.error(f"Error getting tasks: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to get tasks: {str(e)}"
        ) from e


@router.get("/cases/{case_id}/tasks", response_model=List[Task])
async def get_tasks_for_case(
    case_id: UUID,
    task_repo: TaskRepository = Depends(get_task_repository),
    _: bool = Depends(validate_api_key),
) -> List[Task]:
    """
    Get all tasks for a case.

    Args:
        case_id: The case ID
        task_repo: Task repository for database access
        _: API key validation result (unused)

    Returns:
        List[Task]: The tasks

    Raises:
        HTTPException: If an error occurs
    """
    try:
        log_with_context(logger, "info", "Getting tasks for case", case_id=str(case_id))

        # Get tasks using the typed repository
        tasks = await task_repo.get_tasks_by_case(case_id)
        return tasks

    except Exception as e:
        logger.error(f"Error getting tasks for case: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to get tasks for case: {str(e)}"
        ) from e


@router.post("/tasks", response_model=Task)
async def create_task(
    task_create: TaskCreate = Body(...),
    task_repo: TaskRepository = Depends(get_task_repository),
    _: bool = Depends(validate_api_key),
) -> Task:
    """
    Create a new task.

    Args:
        task_create: The task creation data
        task_repo: Task repository for database access
        _: API key validation result (unused)

    Returns:
        Task: The created task

    Raises:
        HTTPException: If an error occurs
    """
    try:
        log_with_context(
            logger,
            "info",
            "Creating new task",
            tenant_id=str(task_create.tenant_id),
            title=task_create.title,
        )

        # Use the strongly-typed repository method
        created_task = await task_repo.create_task(task_create)
        return created_task

    except Exception as e:
        logger.error(f"Error creating task: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to create task: {str(e)}"
        ) from e


@router.post("/cases/{case_id}/tasks", response_model=Task)
async def create_task_for_case(
    case_id: UUID,
    task_create: TaskCreate = Body(...),
    task_service: TaskService = Depends(get_task_service),
    _: bool = Depends(validate_api_key),
) -> Task:
    """
    Create a new task for a specific case, using the service layer.

    Args:
        case_id: The case ID
        task_create: The task creation data
        task_service: Task service for coordinated operations
        _: API key validation result (unused)

    Returns:
        Task: The created task

    Raises:
        HTTPException: If an error occurs
    """
    try:
        log_with_context(
            logger,
            "info",
            "Creating new task for case",
            tenant_id=str(task_create.tenant_id),
            case_id=str(case_id),
            title=task_create.title,
        )

        # Use the service layer to create a task with additional validation
        created_task = await task_service.create_task_for_case(task_create, case_id)
        return created_task

    except ValueError as e:
        # Handle validation errors from the service layer
        raise HTTPException(status_code=400, detail=str(e)) from e
    except Exception as e:
        logger.error(f"Error creating task for case: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to create task for case: {str(e)}"
        ) from e


@router.put("/tasks/{task_id}", response_model=Task)
async def update_task(
    task_id: UUID,
    task_update: TaskUpdate = Body(...),
    task_repo: TaskRepository = Depends(get_task_repository),
    _: bool = Depends(validate_api_key),
) -> Task:
    """
    Update an existing task.

    Args:
        task_id: The task ID
        task_update: The task update data
        task_repo: Task repository for database access
        _: API key validation result (unused)

    Returns:
        Task: The updated task

    Raises:
        HTTPException: If task not found or an error occurs
    """
    try:
        log_with_context(logger, "info", "Updating task", task_id=str(task_id))

        # Use the strongly-typed repository method
        updated_task = await task_repo.update_task(task_id, task_update)

        if not updated_task:
            raise HTTPException(status_code=404, detail="Task not found")
        return updated_task

    except HTTPException:
        raise  # Re-raise HTTP exceptions
    except Exception as e:
        logger.error(f"Error updating task: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to update task: {str(e)}"
        ) from e


@router.put("/tasks/{task_id}/status", response_model=Task)
async def update_task_status(
    task_id: UUID,
    status: TaskStatus = Body(..., embed=True),
    task_repo: TaskRepository = Depends(get_task_repository),
    _: bool = Depends(validate_api_key),
) -> Task:
    """
    Update a task's status.

    Args:
        task_id: The task ID
        status: The new task status
        task_repo: Task repository for database access
        _: API key validation result (unused)

    Returns:
        Task: The updated task

    Raises:
        HTTPException: If task not found or an error occurs
    """
    try:
        log_with_context(
            logger, "info", f"Updating task status to {status}", task_id=str(task_id)
        )

        # Get current task to determine if we need to set completion date
        task = await task_repo.get_by_id(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        # Create task update with all required fields
        task_update = TaskUpdate(
            status=status,
            # Add all required fields with None value to satisfy type checking
            title=None,
            description=None,
            priority=None,
            due_date=None,
            assigned_to=None,
            completed_at=None,
            completed_by=None,
            case_id=None,
            updated_by=None,  # This API uses API key validation, no current_user
            metadata=None,
        )

        # Set completion date if new status is COMPLETED
        if status == TaskStatus.COMPLETED and task.status != TaskStatus.COMPLETED:
            task_update.completed_at = datetime.now()
            # This API uses API key validation, no current_user
            task_update.completed_by = None

        # Use the repository method
        updated_task = await task_repo.update_task(task_id, task_update)
        return updated_task

    except HTTPException:
        raise  # Re-raise HTTP exceptions
    except Exception as e:
        logger.error(f"Error updating task status: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to update task status: {str(e)}"
        ) from e


@router.put("/tasks/{task_id}/reassign", response_model=Task)
async def reassign_task(
    task_id: UUID,
    user_id: UUID = Body(..., embed=True),
    task_service: TaskService = Depends(get_task_service),
    _: bool = Depends(validate_api_key),
) -> Task:
    """
    Reassign a task to a different user, using the service layer.

    Args:
        task_id: The task ID
        user_id: The user ID to assign the task to
        task_service: Task service for coordinated operations
        _: API key validation result (unused)

    Returns:
        Task: The updated task

    Raises:
        HTTPException: If task not found, user not found, or an error occurs
    """
    try:
        log_with_context(
            logger,
            "info",
            "Reassigning task",
            task_id=str(task_id),
            user_id=str(user_id),
        )

        # Use the service layer to reassign a task with additional validation
        updated_task = await task_service.reassign_task(task_id, user_id)
        return updated_task

    except ValueError as e:
        # Handle validation errors from the service layer
        raise HTTPException(status_code=400, detail=str(e)) from e
    except Exception as e:
        logger.error(f"Error reassigning task: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to reassign task: {str(e)}"
        ) from e


@router.get("/tasks/overdue", response_model=List[Task])
async def get_overdue_tasks(
    tenant_id: UUID,
    task_service: TaskService = Depends(get_task_service),
    _: bool = Depends(validate_api_key),
) -> List[Task]:
    """
    Get all overdue tasks for a tenant, using the service layer.

    Args:
        tenant_id: The tenant ID
        task_service: Task service for coordinated operations
        _: API key validation result (unused)

    Returns:
        List[Task]: The overdue tasks

    Raises:
        HTTPException: If an error occurs
    """
    try:
        log_with_context(
            logger, "info", "Getting overdue tasks", tenant_id=str(tenant_id)
        )

        # Use the service layer to get overdue tasks
        overdue_tasks = await task_service.get_overdue_tasks(tenant_id)
        return overdue_tasks

    except Exception as e:
        logger.error(f"Error getting overdue tasks: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to get overdue tasks: {str(e)}"
        ) from e
