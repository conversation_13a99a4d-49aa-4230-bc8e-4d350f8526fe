"""
Task API endpoints for the PI Lawyer application.

This module provides REST API endpoints for managing Task entities,
including CRUD operations with proper authentication and authorization.
"""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Path, Query
from pydantic import BaseModel

from pi_lawyer.api.auth import get_current_user, require_role, validate_api_key
from pi_lawyer.data import TaskRepository, get_task_repository
from pi_lawyer.models import Task, TaskCreate, TaskUpdate, UserProfile
from pi_lawyer.models.task import TaskStatus
from pi_lawyer.utils.structured_logging import get_logger

# Set up the router
router = APIRouter(
    prefix="/api/tasks",
    tags=["tasks"],
    responses={
        404: {"description": "Task not found"},
        403: {"description": "Not authorized"},
    },
)

# Service router for internal service-to-service communication
service_router = APIRouter(
    prefix="/api/service/tasks",
    tags=["tasks"],
    responses={
        404: {"description": "Task not found"},
        403: {"description": "Not authorized"},
    },
)

# Set up logging
logger = get_logger(__name__)


def ensure_complete_task_update(task_update: TaskUpdate) -> TaskUpdate:
    """
    Ensure the TaskUpdate object has all fields set to satisfy type checking.
    If a field is not present or is Ellipsis, set it to None.

    Args:
        task_update: The task update object to validate

    Returns:
        The validated task update object with all fields set
    """
    # Create a new TaskUpdate with all fields explicitly set
    complete_update = TaskUpdate(
        title=getattr(task_update, "title", None),
        description=getattr(task_update, "description", None),
        status=getattr(task_update, "status", None),
        priority=getattr(task_update, "priority", None),
        due_date=getattr(task_update, "due_date", None),
        assigned_to=getattr(task_update, "assigned_to", None),
        completed_at=getattr(task_update, "completed_at", None),
        completed_by=getattr(task_update, "completed_by", None),
        case_id=getattr(task_update, "case_id", None),
        updated_by=getattr(task_update, "updated_by", None),
        metadata=getattr(task_update, "metadata", None),
    )

    return complete_update


# Query parameters model
class TaskFilterParams(BaseModel):
    """Parameters for filtering tasks."""

    status: Optional[str] = None
    case_id: Optional[UUID] = None
    assigned_to: Optional[UUID] = None
    priority: Optional[str] = None
    due_date_before: Optional[datetime] = None
    due_date_after: Optional[datetime] = None
    created_after: Optional[datetime] = None
    created_before: Optional[datetime] = None
    updated_after: Optional[datetime] = None
    updated_before: Optional[datetime] = None
    created_by: Optional[UUID] = None
    updated_by: Optional[UUID] = None
    order_by: Optional[str] = None
    order_direction: Optional[str] = None
    limit: int = Query(default=50, ge=1, le=100)
    offset: int = Query(default=0, ge=0)


@router.get("", response_model=List[Task])
async def list_tasks(
    filter_params: TaskFilterParams = Depends(),
    current_user: UserProfile = Depends(get_current_user),
    task_repo: TaskRepository = Depends(get_task_repository),
) -> List[Task]:
    """
    List tasks with optional filtering parameters.
    Tasks are filtered by tenant_id for tenant isolation.
    """
    logger.info(
        "List tasks request",
        extra={
            "user_id": str(current_user.id),
            "tenant_id": str(current_user.tenant_id),
            "filters": filter_params.dict(exclude_none=True),
        },
    )

    # Enforce tenant isolation
    tasks = await task_repo.list(
        tenant_id=current_user.tenant_id,
        status=filter_params.status,
        case_id=filter_params.case_id,
        assigned_to=filter_params.assigned_to,
        priority=filter_params.priority,
        due_date_before=filter_params.due_date_before,
        due_date_after=filter_params.due_date_after,
        limit=filter_params.limit,
        offset=filter_params.offset,
    )

    return tasks


@router.get("/my", response_model=List[Task])
async def list_my_tasks(
    filter_params: TaskFilterParams = Depends(),
    current_user: UserProfile = Depends(get_current_user),
    task_repo: TaskRepository = Depends(get_task_repository),
) -> List[Task]:
    """
    List tasks assigned to the current user.
    Provides a convenient endpoint for users to see their own tasks.
    """
    logger.info(
        "List my tasks request",
        extra={
            "user_id": str(current_user.id),
            "tenant_id": str(current_user.tenant_id),
            "filters": filter_params.dict(exclude_none=True),
        },
    )

    # Force assigned_to to be the current user
    filter_params.assigned_to = current_user.id

    # Enforce tenant isolation
    tasks = await task_repo.list(
        tenant_id=current_user.tenant_id,
        status=filter_params.status,
        case_id=filter_params.case_id,
        assigned_to=filter_params.assigned_to,
        priority=filter_params.priority,
        due_date_before=filter_params.due_date_before,
        due_date_after=filter_params.due_date_after,
        limit=filter_params.limit,
        offset=filter_params.offset,
    )

    return tasks


@router.get("/{task_id}", response_model=Task)
async def get_task(
    task_id: UUID = Path(..., description="The ID of the task to retrieve"),
    current_user: UserProfile = Depends(get_current_user),
    task_repo: TaskRepository = Depends(get_task_repository),
) -> Task:
    """
    Get a single task by ID.
    Ensures the user can only access tasks within their tenant.
    """
    logger.info(
        "Get task request",
        extra={
            "task_id": str(task_id),
            "user_id": str(current_user.id),
            "tenant_id": str(current_user.tenant_id),
        },
    )

    task = await task_repo.get_by_id(task_id)

    if not task:
        logger.warning("Task not found", extra={"task_id": str(task_id)})
        raise HTTPException(status_code=404, detail="Task not found")
    # Enforce tenant isolation
    if task.tenant_id != current_user.tenant_id:
        logger.warning(
            "Unauthorized access attempt",
            extra={
                "task_id": str(task_id),
                "user_id": str(current_user.id),
                "user_tenant": str(current_user.tenant_id),
                "task_tenant": str(task.tenant_id),
            },
        )
        raise HTTPException(
            status_code=403, detail="Not authorized to access this task"
        )
    return task


@router.post("", response_model=Task, status_code=201)
async def create_task(
    task_data: TaskCreate,
    current_user: UserProfile = Depends(get_current_user),
    task_repo: TaskRepository = Depends(get_task_repository),
) -> Task:
    """
    Create a new task.
    Auto-assigns the current user's tenant_id for tenant isolation.
    """
    logger.info(
        "Create task request",
        extra={
            "user_id": str(current_user.id),
            "tenant_id": str(current_user.tenant_id),
        },
    )

    # Force tenant isolation
    task_data.tenant_id = current_user.tenant_id
    # Record the creator
    task_data.created_by = current_user.id

    try:
        new_task = await task_repo.create(task_data)
        logger.info("Task created successfully", extra={"task_id": str(new_task.id)})
        return new_task
    except Exception as e:
        logger.error(f"Error creating task: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to create task: {str(e)}"
        ) from e


@router.put("/{task_id}", response_model=Task)
async def update_task(
    task_update: TaskUpdate,
    task_id: UUID = Path(..., description="The ID of the task to update"),
    current_user: UserProfile = Depends(get_current_user),
    task_repo: TaskRepository = Depends(get_task_repository),
) -> Task:
    """
    Update an existing task.
    Ensures the user can only update tasks within their tenant.
    """
    logger.info(
        "Update task request",
        extra={
            "task_id": str(task_id),
            "user_id": str(current_user.id),
            "tenant_id": str(current_user.tenant_id),
        },
    )

    # Check if task exists and belongs to user's tenant
    existing_task = await task_repo.get_by_id(task_id)

    if not existing_task:
        logger.warning("Task not found for update", extra={"task_id": str(task_id)})
        raise HTTPException(status_code=404, detail="Task not found")
    # Enforce tenant isolation
    if existing_task.tenant_id != current_user.tenant_id:
        logger.warning(
            "Unauthorized update attempt",
            extra={
                "task_id": str(task_id),
                "user_id": str(current_user.id),
                "user_tenant": str(current_user.tenant_id),
                "task_tenant": str(existing_task.tenant_id),
            },
        )
        raise HTTPException(
            status_code=403, detail="Not authorized to update this task"
        )
    # Additional permission check: only assigned user, creator, or managers can update
    is_assigned = existing_task.assigned_to == current_user.id
    is_creator = existing_task.created_by == current_user.id
    is_manager = current_user.role in ["partner", "attorney"]

    if not (is_assigned or is_creator or is_manager):
        logger.warning(
            "User not authorized to modify this task",
            extra={
                "task_id": str(task_id),
                "user_id": str(current_user.id),
            },
        )
        raise HTTPException(
            status_code=403,
            detail=(
                "You are not authorized to modify this task. Only the assigned "
                "user, creator, or managers can update tasks."
            ),
        )
    # Record who made the update
    task_update.updated_by = current_user.id

    # Ensure all fields are properly set for type checking
    complete_update = ensure_complete_task_update(task_update)

    try:
        updated_task = await task_repo.update(task_id, complete_update)
        logger.info("Task updated successfully", extra={"task_id": str(task_id)})
        return updated_task
    except Exception as e:
        logger.error(f"Error updating task: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to update task: {str(e)}"
        ) from e


@router.put("/{task_id}/complete", response_model=Task)
async def complete_task(
    task_id: UUID = Path(..., description="The ID of the task to mark as complete"),
    current_user: UserProfile = Depends(get_current_user),
    task_repo: TaskRepository = Depends(get_task_repository),
) -> Task:
    """
    Mark a task as complete.
    Only the assigned user or a manager can complete a task.
    """
    logger.info(
        "Complete task request",
        extra={
            "task_id": str(task_id),
            "user_id": str(current_user.id),
            "tenant_id": str(current_user.tenant_id),
        },
    )

    # Check if task exists and belongs to user's tenant
    existing_task = await task_repo.get_by_id(task_id)

    if not existing_task:
        logger.warning("Task not found for completion", extra={"task_id": str(task_id)})
        raise HTTPException(status_code=404, detail="Task not found")
    # Enforce tenant isolation
    if existing_task.tenant_id != current_user.tenant_id:
        logger.warning(
            "Unauthorized completion attempt",
            extra={
                "task_id": str(task_id),
                "user_id": str(current_user.id),
                "user_tenant": str(current_user.tenant_id),
                "task_tenant": str(existing_task.tenant_id),
            },
        )
        raise HTTPException(
            status_code=403, detail="Not authorized to complete this task"
        )
    # Additional permission check: only the assigned user or managers can complete tasks
    is_assigned = existing_task.assigned_to == current_user.id
    is_manager = current_user.role in ["partner", "attorney"]

    if not (is_assigned or is_manager):
        logger.warning(
            "User not authorized to complete this task",
            extra={
                "task_id": str(task_id),
                "user_id": str(current_user.id),
            },
        )
        raise HTTPException(
            status_code=403,
            detail=(
                "You are not authorized to complete this task. Only the assigned "
                "user or managers can mark tasks as complete."
            ),
        )
    # Create update data

    update_data = TaskUpdate(
        status=TaskStatus.COMPLETED,
        completed_at=datetime.now(),
        completed_by=current_user.id,
        updated_by=current_user.id,
        # Add required fields with None value to satisfy type checking
        title=None,
        description=None,
        priority=None,
        due_date=None,
        assigned_to=None,
        case_id=None,
        metadata=None,
    )

    try:
        updated_task = await task_repo.update(task_id, update_data)
        logger.info("Task marked as complete", extra={"task_id": str(task_id)})
        return updated_task
    except Exception as e:
        logger.error(f"Error completing task: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to complete task: {str(e)}"
        ) from e


@router.delete("/{task_id}", status_code=204)
async def delete_task(
    task_id: UUID = Path(..., description="The ID of the task to delete"),
    current_user: UserProfile = Depends(get_current_user),
    task_repo: TaskRepository = Depends(get_task_repository),
    _: UserProfile = Depends(require_role(["partner", "attorney"])),
) -> None:
    """
    Delete a task.
    Requires partner or attorney role.
    Ensures the user can only delete tasks within their tenant.
    """
    logger.info(
        "Delete task request",
        extra={
            "task_id": str(task_id),
            "user_id": str(current_user.id),
            "tenant_id": str(current_user.tenant_id),
        },
    )

    # Check if task exists and belongs to user's tenant
    existing_task = await task_repo.get_by_id(task_id)

    if not existing_task:
        logger.warning("Task not found for deletion", extra={"task_id": str(task_id)})
        raise HTTPException(status_code=404, detail="Task not found")
    # Enforce tenant isolation
    if existing_task.tenant_id != current_user.tenant_id:
        logger.warning(
            "Unauthorized deletion attempt",
            extra={
                "task_id": str(task_id),
                "user_id": str(current_user.id),
                "user_tenant": str(current_user.tenant_id),
                "task_tenant": str(existing_task.tenant_id),
            },
        )
        raise HTTPException(
            status_code=403, detail="Not authorized to delete this task"
        )
    try:
        await task_repo.delete(task_id)
        logger.info("Task deleted successfully", extra={"task_id": str(task_id)})
    except Exception as e:
        logger.error(f"Error deleting task: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to delete task: {str(e)}"
        ) from e


# Service endpoints for internal service-to-service communication


@service_router.get("/{task_id}", response_model=Task)
async def get_task_service(
    task_id: UUID = Path(..., description="The ID of the task to retrieve"),
    task_repo: TaskRepository = Depends(get_task_repository),
    _: bool = Depends(validate_api_key),
) -> Task:
    """
    Service endpoint to get a single task by ID.
    This endpoint is for internal service-to-service communication.
    It requires a valid service API key.
    """
    logger.info("Service get task request", extra={"task_id": str(task_id)})

    task = await task_repo.get_by_id(task_id)

    if not task:
        logger.warning(
            "Task not found in service request", extra={"task_id": str(task_id)}
        )
        raise HTTPException(status_code=404, detail="Task not found")
    return task


@service_router.post("", response_model=Task, status_code=201)
async def create_task_service(
    task_data: TaskCreate,
    task_repo: TaskRepository = Depends(get_task_repository),
    _: bool = Depends(validate_api_key),
) -> Task:
    """
    Service endpoint to create a new task.
    This endpoint is for internal service-to-service communication.
    It requires a valid service API key.
    """
    logger.info(
        "Service create task request",
        extra={
            "tenant_id": str(task_data.tenant_id),
        },
    )

    try:
        new_task = await task_repo.create(task_data)
        logger.info(
            "Task created successfully via service",
            extra={"task_id": str(new_task.id)},
        )
        return new_task
    except Exception as e:
        logger.error(f"Error creating task via service: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to create task: {str(e)}"
        ) from e
