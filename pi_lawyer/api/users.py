"""
User API endpoints for the PI Lawyer application.

This module provides REST API endpoints for managing User entities,
including CRUD operations with proper authentication and authorization.
"""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Path, Query
from pydantic import BaseModel, EmailStr

from pi_lawyer.api.auth import get_current_user, require_role, validate_api_key
from pi_lawyer.data import UserProfileRepository, get_user_profile_repository
from pi_lawyer.models import UserProfile, UserProfileCreate, UserProfileUpdate
from pi_lawyer.utils.structured_logging import get_logger

# Set up the router
router = APIRouter(
    prefix="/api/users",
    tags=["users"],
    responses={
        404: {"description": "User not found"},
        403: {"description": "Not authorized"},
    },
)

# Service router for internal service-to-service communication
service_router = APIRouter(
    prefix="/api/service/users",
    tags=["users"],
    responses={
        404: {"description": "User not found"},
        403: {"description": "Not authorized"},
    },
)

# Set up logging
logger = get_logger(__name__)


def ensure_complete_user_update(user_update: UserProfileUpdate) -> UserProfileUpdate:
    """
    Ensure the UserProfileUpdate object has all fields set to satisfy type checking.
    If a field is not present or is Ellipsis, set it to None.

    Args:
        user_update: The user update object to validate

    Returns:
        The validated user update object with all fields set
    """
    # Create a new UserProfileUpdate with all fields explicitly set
    complete_update = UserProfileUpdate(
        email=getattr(user_update, "email", None),
        full_name=getattr(user_update, "full_name", None),
        role=getattr(user_update, "role", None),
        profile_image_url=getattr(user_update, "profile_image_url", None),
        phone=getattr(user_update, "phone", None),
        job_title=getattr(user_update, "job_title", None),
        bio=getattr(user_update, "bio", None),
        preferences=getattr(user_update, "preferences", None),
        is_active=getattr(user_update, "is_active", None),
        metadata=getattr(user_update, "metadata", None),
        tenant_id=getattr(user_update, "tenant_id", None),
        updated_by=getattr(user_update, "updated_by", None),
    )

    return complete_update


# Query parameters model
class UserFilterParams(BaseModel):
    """Parameters for filtering users."""

    role: Optional[str] = None
    email: Optional[EmailStr] = None
    is_active: Optional[bool] = None
    limit: int = Query(default=50, ge=1, le=100)
    offset: int = Query(default=0, ge=0)


@router.get("", response_model=List[UserProfile])
async def list_users(
    filter_params: UserFilterParams = Depends(),
    current_user: UserProfile = Depends(get_current_user),
    user_repo: UserProfileRepository = Depends(get_user_profile_repository),
    _: UserProfile = Depends(require_role(["partner", "admin"])),
) -> List[UserProfile]:
    """
    List users with optional filtering parameters.
    Requires partner or admin role.
    Users are filtered by tenant_id for tenant isolation.
    """
    logger.info(
        "List users request",
        extra={
            "user_id": str(current_user.id),
            "tenant_id": str(current_user.tenant_id),
            "filters": filter_params.dict(exclude_none=True),
        },
    )

    # Enforce tenant isolation
    users = await user_repo.list_by_tenant(
        tenant_id=current_user.tenant_id,
        is_active=filter_params.is_active,
        role=filter_params.role,
        email=filter_params.email,
        limit=filter_params.limit,
        offset=filter_params.offset,
    )

    return users


@router.get("/me", response_model=UserProfile)
async def get_current_user_profile(
    current_user: UserProfile = Depends(get_current_user),
) -> UserProfile:
    """
    Get the current user's profile.
    This is a convenient endpoint for clients to get the authenticated user's profile.
    """
    logger.info(
        "Get current user profile request",
        extra={
            "user_id": str(current_user.id),
            "tenant_id": str(current_user.tenant_id),
        },
    )

    return current_user


@router.get("/{user_id}", response_model=UserProfile)
async def get_user(
    user_id: UUID = Path(..., description="The ID of the user to retrieve"),
    current_user: UserProfile = Depends(get_current_user),
    user_repo: UserProfileRepository = Depends(get_user_profile_repository),
) -> UserProfile:
    """
    Get a single user by ID.
    Ensures the user can only access users within their tenant.
    """
    logger.info(
        "Get user request",
        extra={
            "requested_user_id": str(user_id),
            "user_id": str(current_user.id),
            "tenant_id": str(current_user.tenant_id),
        },
    )

    # Check if the requested user is the current user
    if user_id == current_user.id:
        return current_user

    # For other users, verify proper permissions
    if current_user.role not in ["partner", "admin"]:
        logger.warning(
            "Unauthorized access attempt to user profile",
            extra={
                "requested_user_id": str(user_id),
                "user_id": str(current_user.id),
                "role": current_user.role,
            },
        )
        raise HTTPException(
            status_code=403, detail="Not authorized to access other user profiles"
        )
    user = await user_repo.get_by_id(user_id)

    if not user:
        logger.warning("User not found", extra={"user_id": str(user_id)})
        raise HTTPException(status_code=404, detail="User not found")
    # Enforce tenant isolation
    if user.tenant_id != current_user.tenant_id:
        logger.warning(
            "Unauthorized access attempt",
            extra={
                "requested_user_id": str(user_id),
                "user_id": str(current_user.id),
                "user_tenant": str(current_user.tenant_id),
                "requested_user_tenant": str(user.tenant_id),
            },
        )
        raise HTTPException(
            status_code=403, detail="Not authorized to access this user"
        )
    return user


@router.post("", response_model=UserProfile, status_code=201)
async def create_user(
    user_data: UserProfileCreate,
    current_user: UserProfile = Depends(get_current_user),
    user_repo: UserProfileRepository = Depends(get_user_profile_repository),
    _: UserProfile = Depends(require_role(["partner", "admin"])),
) -> UserProfile:
    """
    Create a new user.
    Requires partner or admin role.
    Auto-assigns the current user's tenant_id for tenant isolation.
    """
    logger.info(
        "Create user request",
        extra={
            "user_id": str(current_user.id),
            "tenant_id": str(current_user.tenant_id),
        },
    )

    # Force tenant isolation
    user_data.tenant_id = current_user.tenant_id
    # Record the creator
    user_data.created_by = current_user.id

    try:
        # Check if user with this email already exists in the tenant
        if user_data.email:
            existing_users = await user_repo.list_by_tenant(
                tenant_id=current_user.tenant_id, email=user_data.email, limit=1
            )
            if existing_users:
                logger.warning(
                    "User with email already exists",
                    extra={
                        "email": user_data.email,
                        "tenant_id": str(current_user.tenant_id),
                    },
                )
                raise HTTPException(
                    status_code=400,
                    detail=f"User with email {user_data.email} already exists",
                )
        new_user = await user_repo.create(user_data)
        logger.info("User created successfully", extra={"user_id": str(new_user.id)})
        return new_user
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error creating user: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to create user: {str(e)}"
        ) from e


@router.put("/me", response_model=UserProfile)
async def update_current_user(
    user_update: UserProfileUpdate,
    current_user: UserProfile = Depends(get_current_user),
    user_repo: UserProfileRepository = Depends(get_user_profile_repository),
) -> UserProfile:
    """
    Update the current user's profile.
    Users can only update certain fields of their own profile.
    """
    logger.info(
        "Update current user profile request",
        extra={
            "user_id": str(current_user.id),
            "tenant_id": str(current_user.tenant_id),
        },
    )

    # Ensure users can't change critical fields
    if hasattr(user_update, "role") and user_update.role is not None:
        logger.warning(
            "Attempted to change role via self-update",
            extra={
                "user_id": str(current_user.id),
                "current_role": current_user.role,
                "attempted_role": user_update.role,
            },
        )
        raise HTTPException(
            status_code=403, detail="Not authorized to change your own role"
        )
    # Force tenant isolation
    user_update.tenant_id = current_user.tenant_id
    # Record who made the update
    user_update.updated_by = current_user.id

    # Ensure all fields are properly set for type checking
    complete_update = ensure_complete_user_update(user_update)

    try:
        updated_user = await user_repo.update(current_user.id, complete_update)
        logger.info(
            "User updated successfully", extra={"user_id": str(current_user.id)}
        )
        return updated_user
    except Exception as e:
        logger.error(f"Error updating user: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to update user: {str(e)}"
        ) from e


@router.put("/{user_id}", response_model=UserProfile)
async def update_user(
    user_update: UserProfileUpdate,
    user_id: UUID = Path(..., description="The ID of the user to update"),
    current_user: UserProfile = Depends(get_current_user),
    user_repo: UserProfileRepository = Depends(get_user_profile_repository),
    _: UserProfile = Depends(require_role(["partner", "admin"])),
) -> UserProfile:
    """
    Update an existing user.
    Requires partner or admin role.
    Ensures the user can only update users within their tenant.
    """
    logger.info(
        "Update user request",
        extra={
            "target_user_id": str(user_id),
            "user_id": str(current_user.id),
            "tenant_id": str(current_user.tenant_id),
        },
    )

    # Check if user exists and belongs to user's tenant
    existing_user = await user_repo.get_by_id(user_id)

    if not existing_user:
        logger.warning("User not found for update", extra={"user_id": str(user_id)})
        raise HTTPException(status_code=404, detail="User not found")
    # Enforce tenant isolation
    if existing_user.tenant_id != current_user.tenant_id:
        logger.warning(
            "Unauthorized update attempt",
            extra={
                "target_user_id": str(user_id),
                "user_id": str(current_user.id),
                "user_tenant": str(current_user.tenant_id),
                "target_user_tenant": str(existing_user.tenant_id),
            },
        )
        raise HTTPException(
            status_code=403, detail="Not authorized to update this user"
        )
    # Special case: Admins can't change Partner roles
    if (
        current_user.role == "admin"
        and existing_user.role == "partner"
        and user_update.role is not None
        and user_update.role != "partner"
    ):
        logger.warning(
            "Admin attempted to change partner role",
            extra={
                "admin_id": str(current_user.id),
                "partner_id": str(user_id),
                "attempted_role": user_update.role,
            },
        )
        raise HTTPException(
            status_code=403, detail="Admins cannot change Partner roles"
        )
    # Force tenant isolation
    user_update.tenant_id = current_user.tenant_id
    # Record who made the update
    user_update.updated_by = current_user.id

    # Ensure all fields are properly set for type checking
    complete_update = ensure_complete_user_update(user_update)

    try:
        # Check if updating to an email that's already used by another user
        if complete_update.email and complete_update.email != existing_user.email:
            existing_users = await user_repo.list_by_tenant(
                tenant_id=current_user.tenant_id, email=complete_update.email, limit=1
            )
            if existing_users and existing_users[0].id != user_id:
                logger.warning(
                    "User with email already exists",
                    extra={
                        "email": complete_update.email,
                        "tenant_id": str(current_user.tenant_id),
                    },
                )
                raise HTTPException(
                    status_code=400,
                    detail=f"Another user with email {complete_update.email} already exists",
                )
        updated_user = await user_repo.update(user_id, complete_update)
        logger.info("User updated successfully", extra={"user_id": str(user_id)})
        return updated_user
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error updating user: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to update user: {str(e)}"
        ) from e


@router.patch("/{user_id}/deactivate", response_model=UserProfile)
async def deactivate_user(
    user_id: UUID = Path(..., description="The ID of the user to deactivate"),
    current_user: UserProfile = Depends(get_current_user),
    user_repo: UserProfileRepository = Depends(get_user_profile_repository),
    _: UserProfile = Depends(require_role(["partner", "admin"])),
) -> UserProfile:
    """
    Deactivate a user.
    Requires partner or admin role.
    This is safer than deletion and preserves data integrity.
    """
    logger.info(
        "Deactivate user request",
        extra={
            "target_user_id": str(user_id),
            "user_id": str(current_user.id),
            "tenant_id": str(current_user.tenant_id),
        },
    )

    # Check if user exists and belongs to user's tenant
    existing_user = await user_repo.get_by_id(user_id)

    if not existing_user:
        logger.warning(
            "User not found for deactivation", extra={"user_id": str(user_id)}
        )
        raise HTTPException(status_code=404, detail="User not found")
    # Enforce tenant isolation
    if existing_user.tenant_id != current_user.tenant_id:
        logger.warning(
            "Unauthorized deactivation attempt",
            extra={
                "target_user_id": str(user_id),
                "user_id": str(current_user.id),
                "user_tenant": str(current_user.tenant_id),
                "target_user_tenant": str(existing_user.tenant_id),
            },
        )
        raise HTTPException(
            status_code=403, detail="Not authorized to deactivate this user"
        )
    # Prevent self-deactivation
    if user_id == current_user.id:
        logger.warning("Attempted self-deactivation", extra={"user_id": str(user_id)})
        raise HTTPException(
            status_code=400, detail="Cannot deactivate your own account"
        )
    # Prevent admins from deactivating partners
    if current_user.role == "admin" and existing_user.role == "partner":
        logger.warning(
            "Admin attempted to deactivate partner",
            extra={"admin_id": str(current_user.id), "partner_id": str(user_id)},
        )
        raise HTTPException(status_code=403, detail="Admins cannot deactivate Partners")
    # Create update data with all required fields
    update_data = UserProfileUpdate(
        is_active=False,
        updated_by=current_user.id,
        # Add all required fields with None values to satisfy type checking
        email=None,
        full_name=None,
        role=None,
        profile_image_url=None,
        phone=None,
        job_title=None,
        bio=None,
        preferences=None,
        metadata=None,
        tenant_id=None,
    )

    try:
        updated_user = await user_repo.update(user_id, update_data)
        logger.info("User deactivated successfully", extra={"user_id": str(user_id)})
        return updated_user
    except Exception as e:
        logger.error(f"Error deactivating user: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to deactivate user: {str(e)}"
        ) from e


@router.patch("/{user_id}/activate", response_model=UserProfile)
async def activate_user(
    user_id: UUID = Path(..., description="The ID of the user to activate"),
    current_user: UserProfile = Depends(get_current_user),
    user_repo: UserProfileRepository = Depends(get_user_profile_repository),
    _: UserProfile = Depends(require_role(["partner", "admin"])),
) -> UserProfile:
    """
    Activate a previously deactivated user.
    Requires partner or admin role.
    """
    logger.info(
        "Activate user request",
        extra={
            "target_user_id": str(user_id),
            "user_id": str(current_user.id),
            "tenant_id": str(current_user.tenant_id),
        },
    )

    # Check if user exists and belongs to user's tenant
    existing_user = await user_repo.get_by_id(user_id)

    if not existing_user:
        logger.warning("User not found for activation", extra={"user_id": str(user_id)})
        raise HTTPException(status_code=404, detail="User not found")
    # Enforce tenant isolation
    if existing_user.tenant_id != current_user.tenant_id:
        logger.warning(
            "Unauthorized activation attempt",
            extra={
                "target_user_id": str(user_id),
                "user_id": str(current_user.id),
                "user_tenant": str(current_user.tenant_id),
                "target_user_tenant": str(existing_user.tenant_id),
            },
        )
        raise HTTPException(
            status_code=403, detail="Not authorized to activate this user"
        )
    # Create update data with all required fields
    update_data = UserProfileUpdate(
        is_active=True,
        updated_by=current_user.id,
        # Add all required fields with None values to satisfy type checking
        email=None,
        full_name=None,
        role=None,
        profile_image_url=None,
        phone=None,
        job_title=None,
        bio=None,
        preferences=None,
        metadata=None,
        tenant_id=None,
    )

    try:
        updated_user = await user_repo.update(user_id, update_data)
        logger.info("User activated successfully", extra={"user_id": str(user_id)})
        return updated_user
    except Exception as e:
        logger.error(f"Error activating user: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to activate user: {str(e)}"
        ) from e


# Service endpoints for internal service-to-service communication


@service_router.get("/{user_id}", response_model=UserProfile)
async def get_user_service(
    user_id: UUID = Path(..., description="The ID of the user to retrieve"),
    user_repo: UserProfileRepository = Depends(get_user_profile_repository),
    _: bool = Depends(validate_api_key),
) -> UserProfile:
    """
    Service endpoint to get a single user by ID.
    This endpoint is for internal service-to-service communication.
    It requires a valid service API key.
    """
    logger.info("Service get user request", extra={"user_id": str(user_id)})

    user = await user_repo.get_by_id(user_id)

    if not user:
        logger.warning(
            "User not found in service request", extra={"user_id": str(user_id)}
        )
        raise HTTPException(status_code=404, detail="User not found")
    return user


@service_router.get("/auth/{auth_id}", response_model=UserProfile)
async def get_user_by_auth_id_service(
    auth_id: UUID = Path(..., description="The auth ID of the user to retrieve"),
    user_repo: UserProfileRepository = Depends(get_user_profile_repository),
    _: bool = Depends(validate_api_key),
) -> UserProfile:
    """
    Service endpoint to get a single user by auth ID.
    This endpoint is for internal service-to-service communication.
    It requires a valid service API key.
    """
    logger.info("Service get user by auth_id request", extra={"auth_id": str(auth_id)})

    user = await user_repo.get_by_auth_id(auth_id)

    if not user:
        logger.warning(
            "User not found by auth_id in service request",
            extra={"auth_id": str(auth_id)},
        )
        raise HTTPException(status_code=404, detail="User not found")
    return user
