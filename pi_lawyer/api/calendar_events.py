"""
Calendar Events API endpoints for the PI Lawyer application.

This module provides REST API endpoints for managing CalendarEvent entities,
including CRUD operations with proper authentication and authorization.
"""

from datetime import datetime, timed<PERSON>ta
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Path, Query
from pydantic import BaseModel

from pi_lawyer.api.auth import get_current_user, validate_api_key
from pi_lawyer.data import CalendarEventRepository, get_calendar_event_repository
from pi_lawyer.models import (
    CalendarEvent,
    CalendarEventCreate,
    CalendarEventUpdate,
    UserProfile,
)
from pi_lawyer.utils.structured_logging import get_logger

# Set up the router
router = APIRouter(
    prefix="/api/calendar",
    tags=["calendar"],
    responses={
        404: {"description": "Calendar event not found"},
        403: {"description": "Not authorized"},
    },
)

# Service router for internal service-to-service communication
service_router = APIRouter(
    prefix="/api/service/calendar",
    tags=["calendar"],
    responses={
        404: {"description": "Calendar event not found"},
        403: {"description": "Not authorized"},
    },
)

# Set up logging
logger = get_logger(__name__)


# Query parameters model
class CalendarEventFilterParams(BaseModel):
    """Parameters for filtering calendar events."""

    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    event_type: Optional[str] = None
    case_id: Optional[UUID] = None
    client_id: Optional[UUID] = None
    user_id: Optional[UUID] = None
    limit: int = Query(default=50, ge=1, le=100)
    offset: int = Query(default=0, ge=0)


@router.get("", response_model=List[CalendarEvent])
async def list_calendar_events(
    filter_params: CalendarEventFilterParams = Depends(),
    current_user: UserProfile = Depends(get_current_user),
    calendar_repo: CalendarEventRepository = Depends(get_calendar_event_repository),
) -> List[CalendarEvent]:
    """
    List calendar events with optional filtering parameters.
    Events are filtered by tenant_id for tenant isolation.
    """
    logger.info(
        "List calendar events request",
        extra={
            "user_id": str(current_user.id),
            "tenant_id": str(current_user.tenant_id),
            "filters": filter_params.dict(exclude_none=True),
        },
    )

    # Enforce tenant isolation
    events = await calendar_repo.list(
        tenant_id=current_user.tenant_id,
        start_date=filter_params.start_date,
        end_date=filter_params.end_date,
        event_type=filter_params.event_type,
        case_id=filter_params.case_id,
        client_id=filter_params.client_id,
        user_id=filter_params.user_id,
        limit=filter_params.limit,
        offset=filter_params.offset,
    )

    return events


@router.get("/upcoming", response_model=List[CalendarEvent])
async def get_upcoming_events(
    days: int = Query(7, ge=1, le=30, description="Number of days to look ahead"),
    current_user: UserProfile = Depends(get_current_user),
    calendar_repo: CalendarEventRepository = Depends(get_calendar_event_repository),
) -> List[CalendarEvent]:
    """
    Get upcoming calendar events for the next N days.
    A convenient endpoint for dashboards and upcoming event feeds.
    """
    logger.info(
        "Get upcoming events request",
        extra={
            "user_id": str(current_user.id),
            "tenant_id": str(current_user.tenant_id),
            "days": days,
        },
    )

    # Calculate date range
    now = datetime.now()
    end_date = now + timedelta(days=days)

    # Get upcoming events
    events = await calendar_repo.list(
        tenant_id=current_user.tenant_id,
        start_date=now,
        end_date=end_date,
        limit=50,
        order_by="start_datetime",
        order_direction="asc",
    )

    return events


@router.get("/my", response_model=List[CalendarEvent])
async def get_my_events(
    start_date: datetime = Query(None, description="Start date for filtering events"),
    end_date: datetime = Query(None, description="End date for filtering events"),
    limit: int = Query(
        50, ge=1, le=100, description="Maximum number of events to return"
    ),
    current_user: UserProfile = Depends(get_current_user),
    calendar_repo: CalendarEventRepository = Depends(get_calendar_event_repository),
) -> List[CalendarEvent]:
    """
    Get calendar events for the current user.
    A specialized endpoint for getting events assigned to the authenticated user.
    """
    logger.info(
        "Get my events request",
        extra={
            "user_id": str(current_user.id),
            "tenant_id": str(current_user.tenant_id),
            "start_date": start_date,
            "end_date": end_date,
        },
    )

    # Set default date range if not provided
    if not start_date:
        start_date = datetime.now()
    if not end_date:
        end_date = start_date + timedelta(days=30)

    # Get events for the current user
    events = await calendar_repo.list(
        tenant_id=current_user.tenant_id,
        start_date=start_date,
        end_date=end_date,
        user_id=current_user.id,
        limit=limit,
        order_by="start_datetime",
        order_direction="asc",
    )

    return events


@router.get("/case/{case_id}", response_model=List[CalendarEvent])
async def get_case_events(
    case_id: UUID = Path(..., description="The case ID to get events for"),
    current_user: UserProfile = Depends(get_current_user),
    calendar_repo: CalendarEventRepository = Depends(get_calendar_event_repository),
) -> List[CalendarEvent]:
    """
    Get calendar events for a specific case.
    A specialized endpoint for viewing the timeline of a case.
    """
    logger.info(
        "Get case events request",
        extra={
            "case_id": str(case_id),
            "user_id": str(current_user.id),
            "tenant_id": str(current_user.tenant_id),
        },
    )

    # Get case events
    events = await calendar_repo.list(
        tenant_id=current_user.tenant_id,
        case_id=case_id,
        limit=100,
        order_by="start_datetime",
        order_direction="asc",
    )

    return events


@router.get("/{event_id}", response_model=CalendarEvent)
async def get_calendar_event(
    event_id: UUID = Path(..., description="The ID of the calendar event to retrieve"),
    current_user: UserProfile = Depends(get_current_user),
    calendar_repo: CalendarEventRepository = Depends(get_calendar_event_repository),
) -> CalendarEvent:
    """
    Get a single calendar event by ID.
    Ensures the user can only access events within their tenant.
    """
    logger.info(
        "Get calendar event request",
        extra={
            "event_id": str(event_id),
            "user_id": str(current_user.id),
            "tenant_id": str(current_user.tenant_id),
        },
    )

    event = await calendar_repo.get_by_id(event_id)

    if not event:
        logger.warning("Calendar event not found", extra={"event_id": str(event_id)})
        raise HTTPException(status_code=404, detail="Calendar event not found")
    # Enforce tenant isolation
    if event.tenant_id != current_user.tenant_id:
        logger.warning(
            "Unauthorized access attempt",
            extra={
                "event_id": str(event_id),
                "user_id": str(current_user.id),
                "user_tenant": str(current_user.tenant_id),
                "event_tenant": str(event.tenant_id),
            },
        )
        raise HTTPException(
            status_code=403, detail="Not authorized to access this calendar event"
        )
    return event


@router.post("", response_model=CalendarEvent, status_code=201)
async def create_calendar_event(
    event_data: CalendarEventCreate,
    current_user: UserProfile = Depends(get_current_user),
    calendar_repo: CalendarEventRepository = Depends(get_calendar_event_repository),
) -> CalendarEvent:
    """
    Create a new calendar event.
    Auto-assigns the current user's tenant_id for tenant isolation.
    """
    logger.info(
        "Create calendar event request",
        extra={
            "user_id": str(current_user.id),
            "tenant_id": str(current_user.tenant_id),
            "event_type": event_data.event_type,
        },
    )

    # Validate event dates
    if event_data.end_datetime and event_data.start_datetime > event_data.end_datetime:
        raise HTTPException(
            status_code=400, detail="Event end time must be after start time"
        )
    # Force tenant isolation
    event_data.tenant_id = current_user.tenant_id
    # Record the creator
    event_data.created_by = current_user.id

    try:
        new_event = await calendar_repo.create(event_data)
        logger.info(
            "Calendar event created successfully",
            extra={"event_id": str(new_event.id)},
        )
        return new_event
    except Exception as e:
        logger.error(f"Error creating calendar event: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to create calendar event: {str(e)}"
        ) from e


@router.put("/{event_id}", response_model=CalendarEvent)
async def update_calendar_event(
    event_update: CalendarEventUpdate,
    event_id: UUID = Path(..., description="The ID of the calendar event to update"),
    current_user: UserProfile = Depends(get_current_user),
    calendar_repo: CalendarEventRepository = Depends(get_calendar_event_repository),
) -> CalendarEvent:
    """
    Update an existing calendar event.
    Ensures the user can only update events within their tenant.
    """
    logger.info(
        "Update calendar event request",
        extra={
            "event_id": str(event_id),
            "user_id": str(current_user.id),
            "tenant_id": str(current_user.tenant_id),
        },
    )

    # Check if event exists and belongs to user's tenant
    existing_event = await calendar_repo.get_by_id(event_id)

    if not existing_event:
        logger.warning(
            "Calendar event not found for update", extra={"event_id": str(event_id)}
        )
        raise HTTPException(status_code=404, detail="Calendar event not found")
    # Enforce tenant isolation
    if existing_event.tenant_id != current_user.tenant_id:
        logger.warning(
            "Unauthorized update attempt",
            extra={
                "event_id": str(event_id),
                "user_id": str(current_user.id),
                "user_tenant": str(current_user.tenant_id),
                "event_tenant": str(existing_event.tenant_id),
            },
        )
        raise HTTPException(
            status_code=403, detail="Not authorized to update this calendar event"
        )
    # Validate event dates if both are provided
    if event_update.start_datetime and event_update.end_datetime:
        if event_update.start_datetime > event_update.end_datetime:
            raise HTTPException(
                status_code=400, detail="Event end time must be after start time"
            )
    # If only one is provided, check against existing value
    elif event_update.start_datetime:
        if (
            existing_event.end_datetime
            and event_update.start_datetime > existing_event.end_datetime
        ):
            raise HTTPException(
                status_code=400, detail="Event end time must be after start time"
            )
    elif event_update.end_datetime:
        if event_update.end_datetime < existing_event.start_datetime:
            raise HTTPException(
                status_code=400, detail="Event end time must be after start time"
            )
    # Record who made the update
    event_update.updated_by = current_user.id

    try:
        updated_event = await calendar_repo.update(event_id, event_update)
        logger.info(
            "Calendar event updated successfully", extra={"event_id": str(event_id)}
        )
        return updated_event
    except Exception as e:
        logger.error(f"Error updating calendar event: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to update calendar event: {str(e)}"
        ) from e


@router.delete("/{event_id}", status_code=204)
async def delete_calendar_event(
    event_id: UUID = Path(..., description="The ID of the calendar event to delete"),
    current_user: UserProfile = Depends(get_current_user),
    calendar_repo: CalendarEventRepository = Depends(get_calendar_event_repository),
) -> None:
    """
    Delete a calendar event.
    Ensures the user can only delete events within their tenant.
    """
    logger.info(
        "Delete calendar event request",
        extra={
            "event_id": str(event_id),
            "user_id": str(current_user.id),
            "tenant_id": str(current_user.tenant_id),
        },
    )

    # Check if event exists and belongs to user's tenant
    existing_event = await calendar_repo.get_by_id(event_id)

    if not existing_event:
        logger.warning(
            "Calendar event not found for deletion", extra={"event_id": str(event_id)}
        )
        raise HTTPException(status_code=404, detail="Calendar event not found")
    # Enforce tenant isolation
    if existing_event.tenant_id != current_user.tenant_id:
        logger.warning(
            "Unauthorized deletion attempt",
            extra={
                "event_id": str(event_id),
                "user_id": str(current_user.id),
                "user_tenant": str(current_user.tenant_id),
                "event_tenant": str(existing_event.tenant_id),
            },
        )
        raise HTTPException(
            status_code=403, detail="Not authorized to delete this calendar event"
        )
    # Check if the user is the owner of the event or has proper role
    is_owner = existing_event.created_by == current_user.id
    is_manager = current_user.role in ["partner", "attorney", "admin"]
    is_assignee = existing_event.user_id == current_user.id

    if not (is_owner or is_manager or is_assignee):
        logger.warning(
            "Unauthorized deletion attempt - not owner or manager",
            extra={
                "event_id": str(event_id),
                "user_id": str(current_user.id),
                "event_owner": str(existing_event.created_by),
            },
        )
        raise HTTPException(
            status_code=403,
            detail=(
                "Not authorized to delete this calendar event. Only the event "
                "creator, assignee, or managers can delete events."
            ),
        )
    try:
        await calendar_repo.delete(event_id)
        logger.info(
            "Calendar event deleted successfully", extra={"event_id": str(event_id)}
        )
    except Exception as e:
        logger.error(f"Error deleting calendar event: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to delete calendar event: {str(e)}"
        ) from e


# Service endpoints for internal service-to-service communication


@service_router.get("/{event_id}", response_model=CalendarEvent)
async def get_calendar_event_service(
    event_id: UUID = Path(..., description="The ID of the calendar event to retrieve"),
    calendar_repo: CalendarEventRepository = Depends(get_calendar_event_repository),
    _: bool = Depends(validate_api_key),
) -> CalendarEvent:
    """
    Service endpoint to get a single calendar event by ID.
    This endpoint is for internal service-to-service communication.
    It requires a valid service API key.
    """
    logger.info("Service get calendar event request", extra={"event_id": str(event_id)})

    event = await calendar_repo.get_by_id(event_id)

    if not event:
        logger.warning(
            "Calendar event not found in service request",
            extra={"event_id": str(event_id)},
        )
        raise HTTPException(status_code=404, detail="Calendar event not found")
    return event


@service_router.post("", response_model=CalendarEvent, status_code=201)
async def create_calendar_event_service(
    event_data: CalendarEventCreate,
    calendar_repo: CalendarEventRepository = Depends(get_calendar_event_repository),
    _: bool = Depends(validate_api_key),
) -> CalendarEvent:
    """
    Service endpoint to create a new calendar event.
    This endpoint is for internal service-to-service communication.
    It requires a valid service API key.
    """
    logger.info(
        "Service create calendar event request",
        extra={
            "tenant_id": str(event_data.tenant_id),
            "event_type": event_data.event_type,
        },
    )

    # Validate event dates
    if event_data.end_datetime and event_data.start_datetime > event_data.end_datetime:
        raise HTTPException(
            status_code=400, detail="Event end time must be after start time"
        )
    try:
        new_event = await calendar_repo.create(event_data)
        logger.info(
            "Calendar event created successfully via service",
            extra={"event_id": str(new_event.id)},
        )
        return new_event
    except Exception as e:
        logger.error(f"Error creating calendar event via service: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to create calendar event: {str(e)}"
        ) from e


@service_router.post(
    "/generate-deadlines", response_model=List[CalendarEvent], status_code=201
)
async def generate_case_deadlines_service(
    case_id: UUID = Query(..., description="The case ID to generate deadlines for"),
    calendar_repo: CalendarEventRepository = Depends(get_calendar_event_repository),
    _: bool = Depends(validate_api_key),
) -> List[CalendarEvent]:
    """
    Service endpoint to generate deadline events for a case.
    This would typically be called when a case is created or updated with key dates.
    It requires a valid service API key.
    """
    logger.info(
        "Service generate case deadlines request", extra={"case_id": str(case_id)}
    )

    # In a real implementation, this would call a service to analyze the case
    # and generate appropriate deadlines. For now, we'll stub this out.
    logger.info(
        "This is a placeholder for deadline generation. In a real implementation, "
        + "this would calculate deadlines based on case type, jurisdiction, and filing."
    )

    # Return an empty list for now
    return []
