"""
Case API endpoints for PI Lawyer AI.

This module provides FastAPI endpoints for case management operations,
leveraging the type-safe case repository.
"""

import logging
import os
from typing import List
from uuid import UUID

from fastapi import APIRouter, Body, Depends, HTTPException, Path, Security
from fastapi.security import APIKeyHeader

from ..data import CaseRepository, UserProfileRepository
from ..models import Case, CaseCreate, CaseStatus, CaseUpdate
from ..utils.structured_logging import log_with_context

# Set up logging
logger = logging.getLogger(__name__)

# Initialize the router
router = APIRouter(prefix="/api", tags=["cases"])

# Set up API key security - reusing the same pattern as in document_search.py
API_KEY_HEADER = APIKeyHeader(name="Authorization", auto_error=True)


async def validate_api_key(api_key: str = Security(API_KEY_HEADER)) -> bool:
    """
    Validate the API key from the Authorization header.

    Args:
        api_key: The API key from the Authorization header

    Returns:
        bool: Whether the API key is valid

    Raises:
        HTTPException: If the API key is invalid
    """
    expected_key = f"Bearer {os.environ.get('API_SERVICE_TOKEN')}"
    if api_key != expected_key:
        raise HTTPException(status_code=401, detail="Invalid API key")
    return True


# Dependency injection for repositories
def get_case_repository(use_service_role: bool = True) -> CaseRepository:
    """
    Get a CaseRepository instance for dependency injection.

    Args:
        use_service_role: Whether to use service role credentials

    Returns:
        CaseRepository: An instance of the case repository
    """
    return CaseRepository(use_service_role=use_service_role)


def get_user_profile_repository(use_service_role: bool = True) -> UserProfileRepository:
    """
    Get a UserProfileRepository instance for dependency injection.

    Args:
        use_service_role: Whether to use service role credentials

    Returns:
        UserProfileRepository: An instance of the user profile repository
    """
    return UserProfileRepository(use_service_role=use_service_role)


@router.get("/cases", response_model=List[Case])
async def get_cases_for_tenant(
    tenant_id: UUID,
    user_profile_repo: UserProfileRepository = Depends(get_user_profile_repository),
    case_repo: CaseRepository = Depends(get_case_repository),
    _: bool = Depends(validate_api_key),
) -> List[Case]:
    """
    Get all cases for a tenant.

    Args:
        tenant_id: The tenant ID
        user_profile_repo: User profile repository for access control
        case_repo: Case repository for database access
        _: API key validation result (unused)

    Returns:
        List[Case]: The cases

    Raises:
        HTTPException: If an error occurs
    """
    try:
        log_with_context(
            logger, "info", "Getting cases for tenant", tenant_id=str(tenant_id)
        )

        # Get cases using the typed repository
        cases = await case_repo.get_by_tenant_id(tenant_id)
        return cases

    except Exception as e:
        logger.error(f"Error getting cases: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to get cases: {str(e)}"
        ) from e


@router.get("/cases/{case_id}", response_model=Case)
async def get_case(
    case_id: UUID = Path(..., description="The case ID"),
    case_repo: CaseRepository = Depends(get_case_repository),
    _: bool = Depends(validate_api_key),
) -> Case:
    """
    Get a case by ID.

    Args:
        case_id: The case ID
        case_repo: Case repository for database access
        _: API key validation result (unused)

    Returns:
        Case: The case

    Raises:
        HTTPException: If case not found or an error occurs
    """
    try:
        log_with_context(logger, "info", "Getting case by ID", case_id=str(case_id))

        case = await case_repo.get_by_id(case_id)

        if not case:
            raise HTTPException(status_code=404, detail="Case not found")
        return case

    except HTTPException:
        raise  # Re-raise HTTP exceptions
    except Exception as e:
        logger.error(f"Error getting case: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to get case: {str(e)}"
        ) from e


@router.post("/cases", response_model=Case)
async def create_case(
    case_create: CaseCreate = Body(...),
    case_repo: CaseRepository = Depends(get_case_repository),
    _: bool = Depends(validate_api_key),
) -> Case:
    """
    Create a new case.

    Args:
        case_create: The case creation data
        case_repo: Case repository for database access
        _: API key validation result (unused)

    Returns:
        Case: The created case

    Raises:
        HTTPException: If an error occurs
    """
    try:
        log_with_context(
            logger,
            "info",
            "Creating new case",
            tenant_id=str(case_create.tenant_id),
            title=case_create.title,
        )

        # Use the strongly-typed repository method
        created_case = await case_repo.create_case(case_create)
        return created_case

    except Exception as e:
        logger.error(f"Error creating case: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to create case: {str(e)}"
        ) from e


@router.put("/cases/{case_id}", response_model=Case)
async def update_case(
    case_id: UUID = Path(..., description="The case ID"),
    case_update: CaseUpdate = Body(...),
    case_repo: CaseRepository = Depends(get_case_repository),
    _: bool = Depends(validate_api_key),
) -> Case:
    """
    Update an existing case.

    Args:
        case_id: The case ID
        case_update: The case update data
        case_repo: Case repository for database access
        _: API key validation result (unused)

    Returns:
        Case: The updated case

    Raises:
        HTTPException: If case not found or an error occurs
    """
    try:
        log_with_context(logger, "info", "Updating case", case_id=str(case_id))

        # Check if case exists
        existing_case = await case_repo.get_by_id(case_id)
        if not existing_case:
            raise HTTPException(status_code=404, detail="Case not found")
        # Use the strongly-typed repository method
        updated_case = await case_repo.update_case(case_id, case_update)
        return updated_case

    except HTTPException:
        raise  # Re-raise HTTP exceptions
    except Exception as e:
        logger.error(f"Error updating case: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to update case: {str(e)}"
        ) from e


@router.put("/cases/{case_id}/status", response_model=Case)
async def update_case_status(
    case_id: UUID = Path(..., description="The case ID"),
    status: CaseStatus = Body(..., embed=True),
    case_repo: CaseRepository = Depends(get_case_repository),
    _: bool = Depends(validate_api_key),
) -> Case:
    """
    Update a case's status.

    Args:
        case_id: The case ID
        status: The new case status
        case_repo: Case repository for database access
        _: API key validation result (unused)

    Returns:
        Case: The updated case

    Raises:
        HTTPException: If case not found or an error occurs
    """
    try:
        log_with_context(
            logger, "info", f"Updating case status to {status}", case_id=str(case_id)
        )

        # Create a case update with all required fields
        case_update = CaseUpdate(
            status=status,
            updated_by=None,  # This API uses API key validation, no current_user
            # Add required fields with None value to satisfy type checking
            title=None,
            description=None,
            case_type=None,
            sensitive=None,
            client_id=None,
            metadata=None,
        )

        # Use the standard update method from the repository
        updated_case = await case_repo.update_case(case_id, case_update)
        return updated_case

    except Exception as e:
        logger.error(f"Error updating case status: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to update case status: {str(e)}"
        ) from e
