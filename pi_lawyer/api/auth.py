"""
Authentication utilities for PI Lawyer AI API.

This module provides authentication middleware and dependencies for the FastAPI
application, integrating with Supabase JWT authentication.
"""

import logging
import os
from datetime import datetime
from typing import Any, Callable, Dict, List, Optional, Union, cast
from uuid import UUID

import jwt
from fastapi import Depends, Header, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from jwt.exceptions import PyJWTError

from ..data import UserProfileRepository
from ..models import UserProfile, UserRole
from ..utils.enum_helpers import strings_to_enums
from ..utils.structured_logging import log_with_context

# Set up logging
logger = logging.getLogger(__name__)

# Security scheme for Swagger docs
security = HTTPBearer()


class AuthError(Exception):
    """Exception raised for authentication errors."""

    def __init__(self, message: str, status_code: int = 401):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)


class SupabaseJWTAuth:
    """
    Authentication class for validating Supabase JWT tokens.

    This class provides methods for validating JWT tokens issued by Supabase,
    extracting user information, and verifying permissions.
    """

    def __init__(self) -> None:
        """Initialize with Supabase JWT secret from environment variables."""
        self.jwt_secret = os.environ.get("SUPABASE_JWT_SECRET")
        if not self.jwt_secret:
            logger.error("SUPABASE_JWT_SECRET not configured")
            raise ValueError("SUPABASE_JWT_SECRET not configured")

    def decode_token(self, token: str) -> Dict[str, Any]:
        """
        Decode and validate a JWT token.

        Args:
            token: The JWT token to decode

        Returns:
            Dict[str, Any]: The decoded token payload

        Raises:
            AuthError: If the token is invalid
        """
        try:
            # Ensure jwt_secret is not None
            if not self.jwt_secret:
                raise AuthError("JWT secret is not configured")
            # Decode the token
            payload = jwt.decode(
                token,
                self.jwt_secret,  # Now we've verified it's not None
                algorithms=["HS256"],
                options={"verify_signature": True},
            )

            # Verify the token hasn't expired
            if "exp" in payload and datetime.utcnow() > datetime.utcfromtimestamp(
                payload["exp"]
            ):
                raise AuthError("Token has expired")
            # Explicitly cast the return value to the expected type
            return cast(Dict[str, Any], payload)
        except PyJWTError as e:
            logger.error(f"JWT decode error: {str(e)}")
            raise AuthError(f"Invalid token: {str(e)}") from e
        except Exception as e:
            logger.error(f"Unexpected error decoding token: {str(e)}")
            raise AuthError(f"Authentication error: {str(e)}") from e

    def get_auth_id_from_token(self, token: str) -> str:
        """
        Extract the Supabase Auth ID from a JWT token.

        Args:
            token: The JWT token

        Returns:
            str: The Supabase Auth ID (sub claim)

        Raises:
            AuthError: If the token is invalid or doesn't contain a sub claim
        """
        payload = self.decode_token(token)

        # The 'sub' claim contains the user ID
        auth_id = payload.get("sub")
        if not auth_id:
            raise AuthError("Invalid token: Missing user ID")
        # Explicitly cast the return value to ensure it's a string
        return cast(str, auth_id)


# Create an instance of SupabaseJWTAuth
supabase_auth = SupabaseJWTAuth()


# Dependency for getting UserProfileRepository
def get_user_profile_repository() -> UserProfileRepository:
    """Get a UserProfileRepository instance for dependency injection."""
    return UserProfileRepository(use_service_role=True)


async def get_optional_current_user(
    auth_header: Optional[str] = Header(None, alias="Authorization"),
    user_repo: UserProfileRepository = Depends(get_user_profile_repository),
) -> Optional[UserProfile]:
    """
    Get the current user if an authorization header is provided.

    This dependency can be used for endpoints that support but don't require
    authentication.

    Args:
        auth_header: Optional Authorization header
        user_repo: Repository for accessing user profiles

    Returns:
        Optional[UserProfile]: The user profile if authenticated, None otherwise
    """
    if not auth_header or not auth_header.startswith("Bearer "):
        return None

    try:
        # Extract the token
        token = auth_header.replace("Bearer ", "")

        # Get the auth ID from the token
        auth_id = supabase_auth.get_auth_id_from_token(token)

        # Get the user profile
        user = await user_repo.get_by_auth_id(UUID(auth_id))
        return user
    except Exception as e:
        # For optional authentication, just return None on error
        logger.warning(f"Optional authentication failed: {str(e)}")
        return None


async def get_current_user(
    auth_header: str = Header(..., alias="Authorization"),
    user_repo: UserProfileRepository = Depends(get_user_profile_repository),
) -> UserProfile:
    """
    Get the current authenticated user.

    This dependency will raise an exception if authentication fails.

    Args:
        auth_header: Authorization header with Bearer token
        user_repo: Repository for accessing user profiles

    Returns:
        UserProfile: The authenticated user's profile

    Raises:
        HTTPException: If authentication fails
    """
    if not auth_header or not auth_header.startswith("Bearer "):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    try:
        # Extract the token
        token = auth_header.replace("Bearer ", "")

        # Get the auth ID from the token
        auth_id = supabase_auth.get_auth_id_from_token(token)

        # Get the user profile
        user = await user_repo.get_by_auth_id(UUID(auth_id))

        if not user:
            log_with_context(
                logger,
                "warning",
                "User profile not found for authenticated user",
                auth_id=auth_id,
            )
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User profile not found",
            )
        if not user.is_active:
            log_with_context(
                logger,
                "warning",
                "Inactive user attempted to access API",
                user_id=str(user.id),
                auth_id=str(user.auth_id),
            )
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User account is inactive",
            )
        log_with_context(
            logger,
            "info",
            "User authenticated successfully",
            user_id=str(user.id),
            email=user.email,
            role=user.role,
        )

        return user
    except HTTPException:
        raise  # Re-raise HTTP exceptions
    except AuthError as e:
        raise HTTPException(
            status_code=e.status_code,
            detail=e.message,
            headers={"WWW-Authenticate": "Bearer"},
        ) from e
    except Exception as e:
        logger.error(f"Unexpected authentication error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication failed",
            headers={"WWW-Authenticate": "Bearer"},
        ) from e


def require_role(
    allowed_roles: Union[UserRole, str, List[Union[UserRole, str]]]
) -> Callable:
    """
    Dependency factory for role-based access control.

    This creates a dependency that requires the authenticated user to have one
    of the specified roles.

    Args:
        allowed_roles: UserRole or list of UserRole values that are allowed

    Returns:
        callable: A dependency function
    """
    if not isinstance(allowed_roles, list):
        allowed_roles = [allowed_roles]

    # Convert string roles to UserRole enum values
    enum_roles = strings_to_enums(UserRole, allowed_roles)

    async def _require_role(
        current_user: UserProfile = Depends(get_current_user),
    ) -> UserProfile:
        if current_user.role not in enum_roles:
            log_with_context(
                logger,
                "warning",
                "Unauthorized role access attempt",
                user_id=str(current_user.id),
                user_role=current_user.role,
                required_roles=[role.value for role in enum_roles],
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=(
                    f"User with role '{current_user.role}' does not have "
                    "permission to access this resource"
                ),
            )
        return current_user

    return _require_role


def validate_api_key(api_key: str = Header(..., alias="X-API-Key")) -> bool:
    """
    Validate the provided API key against the configured value.

    Args:
        api_key: The API key from the request header

    Returns:
        bool: True if the API key is valid

    Raises:
        HTTPException: If the API key is invalid or missing
    """
    expected_key = os.environ.get("API_KEY")
    if not expected_key:
        logger.error("API_KEY not configured in environment variables")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="API key validation is not properly configured",
        )
    if api_key != expected_key:
        logger.warning(f"Invalid API key provided: {api_key[:5]}...")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid API key"
        )
    return True
