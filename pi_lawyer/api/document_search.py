"""
Document search API endpoint.

This module provides a FastAPI endpoint for searching documents using
vector embeddings in Pinecone. It validates input, enforces access control,
and returns search results.
"""

import logging
from typing import Any, Dict, List, Optional
from uuid import UUID

from fastapi import APIRouter, Body, Depends, HTTPException, status
from pydantic import BaseModel, Field

from ..data import CaseRepository, DocumentRepository
from ..models import Document, UserProfile, UserRole
from ..utils.embeddings import get_embedding_for_text
from ..utils.structured_logging import log_with_context
from .auth import get_current_user, validate_api_key

# Set up logging
logger = logging.getLogger(__name__)

# Initialize the router
router = APIRouter(prefix="/api", tags=["documents"])


class DocumentSearchParams(BaseModel):
    """Parameters for document search requests."""

    query: str = Field(..., description="Search query text")
    document_type: Optional[str] = Field(None, description="Filter by document type")
    limit: int = Field(
        10, ge=1, le=50, description="Maximum number of results to return"
    )
    include_preview: bool = Field(
        True, description="Whether to include content previews in results"
    )


class DocumentSearchResult(BaseModel):
    """Individual document search result."""

    document_id: str = Field(..., description="Document ID")
    score: float = Field(..., description="Search relevance score")
    metadata: Dict[str, Any] = Field(..., description="Document metadata")


class DocumentSearchResponse(BaseModel):
    """Response for document search requests."""

    results: List[DocumentSearchResult] = Field(
        ..., description="List of search results"
    )


# Dependency injection for repositories
def get_document_repository(use_service_role: bool = False) -> DocumentRepository:
    """
    Get a DocumentRepository instance for dependency injection.

    Args:
        use_service_role: Whether to use service role credentials

    Returns:
        DocumentRepository: An instance of the document repository
    """
    return DocumentRepository(use_service_role=use_service_role)


def get_case_repository(use_service_role: bool = False) -> CaseRepository:
    """
    Get a CaseRepository instance for dependency injection.

    Returns:
        CaseRepository: An instance of the case repository
    """
    return CaseRepository(use_service_role=use_service_role)


@router.post("/document_search", response_model=DocumentSearchResponse)
async def search_documents(
    params: DocumentSearchParams = Body(...),
    current_user: UserProfile = Depends(get_current_user),
    doc_repo: DocumentRepository = Depends(get_document_repository),
    case_repo: CaseRepository = Depends(get_case_repository),
) -> DocumentSearchResponse:
    """
    Search for documents using semantic vector search.

    Args:
        params: Search parameters
        current_user: The authenticated user
        doc_repo: Document repository for database access
        case_repo: Case repository for access control

    Returns:
        DocumentSearchResponse: Search results

    Raises:
        HTTPException: If search fails or access is denied
    """
    try:
        # Set context for logging
        log_with_context(
            logger,
            "info",
            f"Document search request: {params.query}",
            tenant_id=str(current_user.tenant_id),
            user_id=str(current_user.id),
        )

        # Build the filter based on access control
        # Use a properly typed dictionary for Pinecone filters with nested structures
        filter_conditions: Dict[str, Any] = {"tenant_id": str(current_user.tenant_id)}

        # Add case access filter if not a partner
        if current_user.role != UserRole.PARTNER:
            # Get cases the user has access to
            # For simplicity in this example, we assume non-partners can only see their assigned cases
            # A real implementation would use more sophisticated access control
            cases = await case_repo.get_by_filter(
                {
                    "tenant_id": str(current_user.tenant_id),
                    "assigned_attorney_id": str(current_user.id),
                }
            )

            accessible_case_ids = [str(case.id) for case in cases]

            if accessible_case_ids:
                filter_conditions["case_id"] = {"$in": accessible_case_ids}
            else:
                # If user has no cases, return empty results
                return DocumentSearchResponse(results=[])

        # Add document type filter if specified
        if params.document_type:
            filter_conditions["document_type"] = params.document_type

        # Generate embedding for the query
        query_embedding = get_embedding_for_text(params.query)

        # DocumentRepository handles Pinecone initialization internally
        # This is more type-safe and encapsulated
        try:
            # Initialize Pinecone through the repository
            # This method initializes the connection but doesn't return a value
            doc_repo._init_pinecone()

            # Now create the index directly
            import pinecone  # Local import to maintain compatibility

            index = pinecone.Index(doc_repo.pinecone_index_name)

            search_response = index.query(
                vector=query_embedding,
                filter=filter_conditions,
                top_k=params.limit,
                include_metadata=True,
            )

            # Transform the results
            results = []
            for match in search_response.matches:
                result = DocumentSearchResult(
                    document_id=match.id, score=match.score, metadata=match.metadata
                )
                results.append(result)

            return DocumentSearchResponse(results=results)

        except Exception as e:
            logger.error(f"Pinecone search error: {str(e)}")
            raise HTTPException(
                status_code=500, detail=f"Search engine error: {str(e)}"
            ) from e

    except Exception as e:
        logger.error(f"Document search error: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Document search failed: {str(e)}"
        ) from e


@router.get("/documents/{document_id}", response_model=Document)
async def get_document(
    document_id: UUID,
    current_user: UserProfile = Depends(get_current_user),
    doc_repo: DocumentRepository = Depends(get_document_repository),
) -> Document:
    """
    Get a document by ID.

    Args:
        document_id: The document ID
        current_user: The authenticated user
        doc_repo: Document repository for database access

    Returns:
        Document: The document

    Raises:
        HTTPException: If document not found or access is denied
    """
    try:
        document = await doc_repo.get_by_id(document_id)

        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        # Check if user has access to this document
        if document.tenant_id != current_user.tenant_id:
            log_with_context(
                logger,
                "warning",
                "Unauthorized access attempt to document",
                document_id=str(document_id),
                user_id=str(current_user.id),
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to access this document",
            )
        return document

    except HTTPException:
        raise  # Re-raise HTTP exceptions
    except Exception as e:
        logger.error(f"Error getting document: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to get document: {str(e)}"
        ) from e


# Create a service API endpoint that uses service token auth
@router.get("/service/documents/{document_id}", response_model=Document)
async def get_document_service(
    document_id: UUID,
    doc_repo: DocumentRepository = Depends(get_document_repository),
    _: bool = Depends(validate_api_key),
) -> Document:
    """
    Get a document by ID (service token auth).

    This endpoint is for server-to-server communication and uses
    service token authentication instead of user authentication.

    Args:
        document_id: The document ID
        doc_repo: Document repository for database access
        _: API key validation result (unused)

    Returns:
        Document: The document

    Raises:
        HTTPException: If document not found
    """
    try:
        log_with_context(
            logger,
            "info",
            "Service API: getting document",
            document_id=str(document_id),
        )

        document = await doc_repo.get_by_id(document_id)

        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        return document

    except HTTPException:
        raise  # Re-raise HTTP exceptions
    except Exception as e:
        logger.error(f"Error getting document: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to get document: {str(e)}"
        ) from e
