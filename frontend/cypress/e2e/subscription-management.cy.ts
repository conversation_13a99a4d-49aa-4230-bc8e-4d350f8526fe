/// <reference path="../support/global.d.ts" />
describe('Subscription Management', () => {
  it('Should navigate to the subscription page', () => {
    cy.visit('/settings/subscription');
    cy.get('body').should('exist');
  });

  it('Should check for subscription page elements', () => {
    cy.visit('/settings/subscription');

    // Check if the page has a heading
    cy.get('h1, h2, h3').should('exist');

    // Check if the page contains subscription-related text
    cy.get('body').then(($body) => {
      const bodyText = $body.text().toLowerCase();
      const hasSubscriptionText =
        bodyText.includes('subscription') ||
        bodyText.includes('plan') ||
        bodyText.includes('billing') ||
        bodyText.includes('payment');

      expect(hasSubscriptionText).to.be.true;
    });
  });

  it('Should navigate to the usage dashboard page', () => {
    cy.visit('/settings/usage');
    cy.get('body').should('exist');
  });

  it('Should check for usage dashboard elements', () => {
    cy.visit('/settings/usage');

    // Check if the page has a heading
    cy.get('h1, h2, h3').should('exist');

    // Check if the page contains usage-related text
    cy.get('body').then(($body) => {
      const bodyText = $body.text().toLowerCase();
      const hasUsageText =
        bodyText.includes('usage') ||
        bodyText.includes('resource') ||
        bodyText.includes('quota') ||
        bodyText.includes('dashboard');

      expect(hasUsageText).to.be.true;
    });
  });
});
