/// <reference path="../../support/global.d.ts" />

describe('Add-on Management', () => {
  beforeEach(() => {
    // Login as an admin user
    cy.login('<EMAIL>', 'password');

    // Visit the subscription management page
    cy.visit('/settings/subscription');

    // Go to the add-ons tab
    cy.contains('button', 'Add-ons').click();
  });

  it('TC-2.1.1: Should add an add-on to a subscription', () => {
    // Click on the "Add Add-on" button for a specific add-on
    cy.get('[data-testid="addon-additional-users"]')
      .contains('button', 'Add').click();

    // Set quantity to 1
    cy.get('[data-testid="addon-quantity"]').clear().type('1');

    // Click on the "Add to Subscription" button
    cy.contains('button', 'Add to Subscription').click();

    // Verify add-on addition
    cy.contains('Add-on added successfully').should('be.visible');
    cy.get('[data-testid="active-addons"]')
      .contains('Additional Users').should('be.visible');
  });

  it('TC-2.1.2: Should add multiple add-ons to a subscription', () => {
    // Add first add-on
    cy.get('[data-testid="addon-advanced-analytics"]')
      .contains('button', 'Add').click();

    // Set quantity to 1
    cy.get('[data-testid="addon-quantity"]').clear().type('1');

    // Click on the "Add to Subscription" button
    cy.contains('button', 'Add to Subscription').click();

    // Verify first add-on addition
    cy.contains('Add-on added successfully').should('be.visible');

    // Add second add-on
    cy.get('[data-testid="addon-priority-support"]')
      .contains('button', 'Add').click();

    // Set quantity to 1
    cy.get('[data-testid="addon-quantity"]').clear().type('1');

    // Click on the "Add to Subscription" button
    cy.contains('button', 'Add to Subscription').click();

    // Verify second add-on addition
    cy.contains('Add-on added successfully').should('be.visible');

    // Verify both add-ons are present
    cy.get('[data-testid="active-addons"]')
      .contains('Advanced Analytics').should('be.visible');
    cy.get('[data-testid="active-addons"]')
      .contains('Priority Support').should('be.visible');
  });

  it('TC-2.2.1: Should remove an add-on from a subscription', () => {
    // Ensure we have an add-on to remove
    cy.get('[data-testid="active-addons"]')
      .contains('Additional Users').should('be.visible');

    // Click on the "Remove" button for the add-on
    cy.get('[data-testid="active-addons"]')
      .contains('Additional Users')
      .parent()
      .contains('button', 'Remove')
      .click();

    // Confirm removal
    cy.contains('button', 'Confirm').click();

    // Verify add-on removal
    cy.contains('Add-on removed successfully').should('be.visible');
    cy.get('[data-testid="active-addons"]')
      .contains('Additional Users').should('not.exist');
  });

  it('TC-2.3.1: Should increase add-on quantity', () => {
    // Ensure we have an add-on to update
    cy.get('[data-testid="active-addons"]')
      .contains('Advanced Analytics').should('be.visible');

    // Click on the "Edit" button for the add-on
    cy.get('[data-testid="active-addons"]')
      .contains('Advanced Analytics')
      .parent()
      .contains('button', 'Edit')
      .click();

    // Get current quantity
    cy.get('[data-testid="addon-quantity"]').invoke('val').then((currentQuantity) => {
      if (currentQuantity == null || currentQuantity === '') {
        throw new Error('Could not retrieve current quantity for add-on.');
      }
      const newQuantity = parseInt(currentQuantity.toString()) + 1;

      // Set new quantity
      cy.get('[data-testid="addon-quantity"]').clear().type(newQuantity.toString());

      // Click on the "Update" button
      cy.contains('button', 'Update').click();

      // Verify quantity update
      cy.contains('Add-on updated successfully').should('be.visible');
      cy.get('[data-testid="active-addons"]')
        .contains('Advanced Analytics')
        .parent()
        .contains(`Quantity: ${newQuantity}`).should('be.visible');
    });
  });

  it('TC-2.3.2: Should decrease add-on quantity', () => {
    // Ensure we have an add-on to update with quantity > 1
    cy.get('[data-testid="active-addons"]')
      .contains('Advanced Analytics').should('be.visible');

    // Click on the "Edit" button for the add-on
    cy.get('[data-testid="active-addons"]')
      .contains('Advanced Analytics')
      .parent()
      .contains('button', 'Edit')
      .click();

    // Get current quantity
    cy.get('[data-testid="addon-quantity"]').invoke('val').then((currentQuantity) => {
      if (currentQuantity == null || currentQuantity === '') {
        throw new Error('Could not retrieve current quantity for add-on.');
      }
      const currentQty = parseInt(currentQuantity.toString());
      if (currentQty > 1) {
        const newQuantity = currentQty - 1;

        // Set new quantity
        cy.get('[data-testid="addon-quantity"]').clear().type(newQuantity.toString());

        // Click on the "Update" button
        cy.contains('button', 'Update').click();

        // Verify quantity update
        cy.contains('Add-on updated successfully').should('be.visible');
        cy.get('[data-testid="active-addons"]')
          .contains('Advanced Analytics')
          .parent()
          .contains(`Quantity: ${newQuantity}`).should('be.visible');
      } else {
        // Skip test if quantity is already 1
        cy.log('Skipping test: Add-on quantity is already 1');
      }
    });
  });

  it('TC-2.3.3: Should remove add-on when quantity is set to zero', () => {
    // Ensure we have an add-on to update
    cy.get('[data-testid="active-addons"]')
      .contains('Priority Support').should('be.visible');

    // Click on the "Edit" button for the add-on
    cy.get('[data-testid="active-addons"]')
      .contains('Priority Support')
      .parent()
      .contains('button', 'Edit')
      .click();

    // Set quantity to 0
    cy.get('[data-testid="addon-quantity"]').clear().type('0');

    // Click on the "Update" button
    cy.contains('button', 'Update').click();

    // Verify add-on removal
    cy.contains('Add-on removed successfully').should('be.visible');
    cy.get('[data-testid="active-addons"]')
      .contains('Priority Support').should('not.exist');
  });
});
