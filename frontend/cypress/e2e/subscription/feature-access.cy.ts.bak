describe('Feature Access Control', () => {
  beforeEach(() => {
    // Login as an admin user
    cy.login('<EMAIL>', 'password');
  });

  it('TC-3.1.1: Should allow access to a feature included in the subscription plan', () => {
    // Visit a page that requires a feature included in the subscription plan
    cy.visit('/documents');

    // Verify that the page loads successfully
    cy.contains('h1', 'Documents').should('be.visible');
    cy.contains('Upload Document').should('be.visible');
  });

  it('TC-3.1.2: Should allow access to a feature included in an add-on', () => {
    // Ensure the user has the Advanced Analytics add-on
    cy.visit('/settings/subscription');
    cy.contains('button', 'Add-ons').click();

    // Check if the add-on is already active
    cy.get('body').then(($body) => {
      if (!$body.text().includes('Advanced Analytics (Active)')) {
        // Add the add-on if not active
        cy.get('[data-testid="addon-advanced-analytics"]')
          .contains('button', 'Add').click();

        cy.get('[data-testid="addon-quantity"]').clear().type('1');
        cy.contains('button', 'Add to Subscription').click();
        cy.contains('Add-on added successfully').should('be.visible');
      }
    });

    // Visit a page that requires the Advanced Analytics feature
    cy.visit('/analytics/advanced');

    // Verify that the page loads successfully
    cy.contains('h1', 'Advanced Analytics').should('be.visible');
    cy.contains('Analytics Dashboard').should('be.visible');
  });

  it('TC-3.1.3: Should deny access to a feature not included in the subscription plan or add-ons', () => {
    // Ensure the user doesn't have the Enterprise Features
    cy.visit('/settings/subscription');

    // Check if the user has the Enterprise plan
    cy.get('body').then(($body) => {
      if ($body.text().includes('Enterprise')) {
        // Change to a lower plan if on Enterprise
        cy.contains('button', 'Change Plan').click();
        cy.get('[data-testid="plan-pro"]').click();
        cy.contains('button', 'Update Subscription').click();
        cy.contains('Subscription updated successfully').should('be.visible');
      }
    });

    // Visit a page that requires Enterprise features
    cy.visit('/enterprise/features');

    // Verify that access is denied
    cy.contains('Access Denied').should('be.visible');
    cy.contains('Your subscription does not include access to this feature').should('be.visible');
    cy.contains('button', 'Upgrade Plan').should('be.visible');
  });

  it('TC-3.2.1: Should allow access to a protected route with an active subscription', () => {
    // Ensure the user has an active subscription
    cy.visit('/settings/subscription');
    cy.contains('Active').should('be.visible');

    // Visit a protected route
    cy.visit('/documents/upload');

    // Verify that the page loads successfully
    cy.contains('h1', 'Upload Document').should('be.visible');
    cy.get('input[type="file"]').should('be.visible');
  });

  it('TC-3.2.2: Should allow access to a protected route with a trial subscription', () => {
    // This test assumes there's a way to switch to a trial subscription
    // For testing purposes, we'll skip this if the user doesn't have a trial subscription

    cy.visit('/settings/subscription');
    cy.get('body').then(($body) => {
      if ($body.text().includes('Trial')) {
        // Visit a protected route
        cy.visit('/documents/upload');

        // Verify that the page loads successfully
        cy.contains('h1', 'Upload Document').should('be.visible');
        cy.get('input[type="file"]').should('be.visible');
      } else {
        cy.log('Skipping test: User does not have a trial subscription');
      }
    });
  });

  it('TC-3.2.3: Should deny access to a protected route with a canceled subscription', () => {
    // This test assumes there's a way to cancel the subscription
    // For testing purposes, we'll skip this if the user doesn't have a canceled subscription

    cy.visit('/settings/subscription');
    cy.get('body').then(($body) => {
      if ($body.text().includes('Canceled')) {
        // Visit a protected route
        cy.visit('/documents/upload');

        // Verify that access is denied
        cy.contains('Subscription Required').should('be.visible');
        cy.contains('Your subscription has been canceled').should('be.visible');
        cy.contains('button', 'Reactivate Subscription').should('be.visible');
      } else {
        cy.log('Skipping test: User does not have a canceled subscription');
      }
    });
  });

  it('TC-3.2.4: Should deny access to a protected route without a subscription', () => {
    // Logout
    cy.logout();

    // Login as a user without a subscription
    cy.login('<EMAIL>', 'password');

    // Visit a protected route
    cy.visit('/documents/upload');

    // Verify that access is denied
    cy.contains('Subscription Required').should('be.visible');
    cy.contains('You do not have an active subscription').should('be.visible');
    cy.contains('button', 'View Plans').should('be.visible');
  });
});
