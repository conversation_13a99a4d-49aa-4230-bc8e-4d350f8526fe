/// <reference path="../../support/global.d.ts" />
describe('Usage Tracking', () => {
  beforeEach(() => {
    // Login as an admin user
    cy.login('<EMAIL>', 'password');
  });

  it('TC-4.1.1: Should track document upload usage', () => {
    // Visit the document upload page
    cy.visit('/documents/upload');

    // Upload a document
    cy.get('input[type="file"]').attachFile('test-document.pdf');
    cy.contains('button', 'Upload').click();

    // Verify upload success
    cy.contains('Document uploaded successfully').should('be.visible');

    // Visit the usage dashboard
    cy.visit('/settings/usage');

    // Select document upload resource type
    cy.get('[data-testid="resource-type-select"]').click();
    cy.contains('Document Uploads').click();

    // Verify that usage has been tracked
    cy.get('[data-testid="current-usage"]').should('not.contain', '0');
  });

  it('TC-4.1.2: Should track document processing usage', () => {
    // Visit the document processing page
    cy.visit('/documents');

    // Find a document and click on "Process"
    cy.contains('test-document.pdf')
      .parent()
      .contains('button', 'Process')
      .click();

    // Select processing type
    cy.contains('Extract Text').click();
    cy.contains('button', 'Start Processing').click();

    // Verify processing success
    cy.contains('Processing completed successfully').should('be.visible');

    // Visit the usage dashboard
    cy.visit('/settings/usage');

    // Select document processing resource type
    cy.get('[data-testid="resource-type-select"]').click();
    cy.contains('Document Processing').click();

    // Verify that usage has been tracked
    cy.get('[data-testid="current-usage"]').should('not.contain', '0');
  });

  it('TC-4.1.3: Should track API call usage', () => {
    // Visit the API page
    cy.visit('/settings/api');

    // Make a test API call
    cy.contains('button', 'Test API').click();

    // Verify API call success
    cy.contains('API call successful').should('be.visible');

    // Visit the usage dashboard
    cy.visit('/settings/usage');

    // Select API calls resource type
    cy.get('[data-testid="resource-type-select"]').click();
    cy.contains('API Calls').click();

    // Verify that usage has been tracked
    cy.get('[data-testid="current-usage"]').should('not.contain', '0');
  });

  it('TC-4.1.4: Should track storage usage', () => {
    // Visit the document upload page
    cy.visit('/documents/upload');

    // Upload a large document
    cy.get('input[type="file"]').attachFile('large-document.pdf');
    cy.contains('button', 'Upload').click();

    // Verify upload success
    cy.contains('Document uploaded successfully').should('be.visible');

    // Visit the usage dashboard
    cy.visit('/settings/usage');

    // Select storage usage resource type
    cy.get('[data-testid="resource-type-select"]').click();
    cy.contains('Storage Usage').click();

    // Verify that usage has been tracked
    cy.get('[data-testid="current-usage"]').should('not.contain', '0');
  });

  it('TC-4.2.1: Should get current period usage for a specific resource type', () => {
    // Visit the usage dashboard
    cy.visit('/settings/usage');

    // Select document upload resource type
    cy.get('[data-testid="resource-type-select"]').click();
    cy.contains('Document Uploads').click();

    // Verify that current period usage is displayed
    cy.get('[data-testid="current-period-usage"]').should('be.visible');
    cy.get('[data-testid="current-usage"]').should('be.visible');
  });

  it('TC-4.2.2: Should get usage history for a specific resource type', () => {
    // Visit the usage dashboard
    cy.visit('/settings/usage');

    // Select document upload resource type
    cy.get('[data-testid="resource-type-select"]').click();
    cy.contains('Document Uploads').click();

    // Click on the "Chart" tab
    cy.contains('button', 'Chart').click();

    // Verify that usage history chart is displayed
    cy.get('[data-testid="usage-chart"]').should('be.visible');
  });

  it('TC-4.2.3: Should get usage for all resource types', () => {
    // Visit the usage dashboard
    cy.visit('/settings/usage');

    // Click on the "Overview" tab
    cy.contains('button', 'Overview').click();

    // Verify that usage for all resource types is displayed
    cy.get('[data-testid="document-upload-usage"]').should('be.visible');
    cy.get('[data-testid="document-processing-usage"]').should('be.visible');
    cy.get('[data-testid="api-calls-usage"]').should('be.visible');
    cy.get('[data-testid="storage-usage"]').should('be.visible');
  });
});
