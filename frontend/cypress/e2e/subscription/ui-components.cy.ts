/// <reference path="../../support/global.d.ts" />
describe('UI Components', () => {
  beforeEach(() => {
    // Login as an admin user
    cy.login('<EMAIL>', 'password');
  });

  describe('Subscription Status Component', () => {
    it('TC-7.1.1: Should display active subscription details', () => {
      // Ensure we have an active subscription
      cy.visit('/settings/subscription');
      cy.get('body').then(($body) => {
        if (!$body.text().includes('Active')) {
          // Create an active subscription if not already active
          cy.contains('button', 'Change Plan').click();
          cy.get('[data-testid="plan-basic"]').click();
          cy.get('[data-testid="billing-cycle-monthly"]').click();
          cy.contains('button', 'Subscribe').click();
          cy.contains('Subscription created successfully').should('be.visible');
        }
      });

      // Visit the dashboard to see the subscription status component
      cy.visit('/dashboard');

      // Verify subscription status component displays active subscription details
      cy.get('[data-testid="subscription-status"]').should('be.visible');
      cy.get('[data-testid="subscription-status"]').contains('Active').should('be.visible');
      cy.get('[data-testid="subscription-plan"]').should('be.visible');
      cy.get('[data-testid="subscription-billing-cycle"]').should('be.visible');
      cy.get('[data-testid="subscription-renews-on"]').should('be.visible');
    });

    it('TC-7.1.2: Should display trial subscription details', () => {
      // This test requires a tenant with a trial subscription
      // For testing purposes, we'll use a special test tenant with a trial

      // Logout
      cy.logout();

      // Login as a user with a trial subscription
      cy.login('<EMAIL>', 'password');

      // Visit the dashboard to see the subscription status component
      cy.visit('/dashboard');

      // Verify subscription status component displays trial subscription details
      cy.get('[data-testid="subscription-status"]').should('be.visible');
      cy.get('[data-testid="subscription-status"]').contains('Trial').should('be.visible');
      cy.get('[data-testid="subscription-plan"]').should('be.visible');
      cy.get('[data-testid="subscription-trial-ends"]').should('be.visible');
      cy.get('[data-testid="subscription-trial-days-remaining"]').should('be.visible');
    });

    it('TC-7.1.3: Should display canceled subscription details', () => {
      // This test requires a tenant with a canceled subscription
      // For testing purposes, we'll use a special test tenant with a canceled subscription

      // Logout
      cy.logout();

      // Login as a user with a canceled subscription
      cy.login('<EMAIL>', 'password');

      // Visit the dashboard to see the subscription status component
      cy.visit('/dashboard');

      // Verify subscription status component displays canceled subscription details
      cy.get('[data-testid="subscription-status"]').should('be.visible');
      cy.get('[data-testid="subscription-status"]').contains('Canceled').should('be.visible');
      cy.get('[data-testid="subscription-plan"]').should('be.visible');
      cy.get('[data-testid="subscription-access-ends"]').should('be.visible');
      cy.get('[data-testid="subscription-reactivate-button"]').should('be.visible');
    });

    it('TC-7.1.4: Should display no subscription state', () => {
      // This test requires a tenant with no subscription
      // For testing purposes, we'll use a special test tenant with no subscription

      // Logout
      cy.logout();

      // Login as a user with no subscription
      cy.login('<EMAIL>', 'password');

      // Visit the dashboard to see the subscription status component
      cy.visit('/dashboard');

      // Verify subscription status component displays no subscription state
      cy.get('[data-testid="subscription-status"]').should('be.visible');
      cy.get('[data-testid="subscription-status"]').contains('No Active Subscription').should('be.visible');
      cy.get('[data-testid="subscription-start-trial-button"]').should('be.visible');
    });
  });

  describe('Usage Dashboard Component', () => {
    it('TC-7.2.1: Should display current usage for all resource types', () => {
      // Visit the usage dashboard
      cy.visit('/settings/usage');

      // Click on the "Overview" tab
      cy.contains('button', 'Overview').click();

      // Verify that usage for all resource types is displayed
      cy.get('[data-testid="document-upload-usage"]').should('be.visible');
      cy.get('[data-testid="document-processing-usage"]').should('be.visible');
      cy.get('[data-testid="api-calls-usage"]').should('be.visible');
      cy.get('[data-testid="storage-usage"]').should('be.visible');
    });

    it('TC-7.2.2: Should display usage history chart', () => {
      // Visit the usage dashboard
      cy.visit('/settings/usage');

      // Click on the "Chart" tab
      cy.contains('button', 'Chart').click();

      // Verify that usage history chart is displayed
      cy.get('[data-testid="usage-chart"]').should('be.visible');
    });

    it('TC-7.2.3: Should filter usage by date range', () => {
      // Visit the usage dashboard
      cy.visit('/settings/usage');

      // Set start date to 3 months ago
      cy.get('[data-testid="start-date-picker"]').click();
      cy.get('.react-datepicker__month-select').select('January');
      cy.get('.react-datepicker__day--001').click();

      // Set end date to current month
      cy.get('[data-testid="end-date-picker"]').click();
      cy.get('.react-datepicker__month-select').select('March');
      cy.get('.react-datepicker__day--031').click();

      // Click on the "Refresh" button
      cy.contains('button', 'Refresh').click();

      // Verify that filtered usage data is displayed
      cy.get('[data-testid="date-range-label"]').should('contain', 'Jan 01 - Mar 31');
    });

    it('TC-7.2.4: Should filter usage by resource type', () => {
      // Visit the usage dashboard
      cy.visit('/settings/usage');

      // Select document upload resource type
      cy.get('[data-testid="resource-type-select"]').click();
      cy.contains('Document Uploads').click();

      // Verify that filtered usage data is displayed
      cy.get('[data-testid="resource-type-label"]').should('contain', 'Document Uploads');
    });
  });

  describe('Subscription Management UI', () => {
    it('TC-7.3.1: Should view available subscription plans', () => {
      // Visit the subscription management page
      cy.visit('/settings/subscription');

      // Click on the "Plans" tab
      cy.contains('button', 'Plans').click();

      // Verify that available plans are displayed
      cy.get('[data-testid="plan-basic"]').should('be.visible');
      cy.get('[data-testid="plan-pro"]').should('be.visible');
      cy.get('[data-testid="plan-enterprise"]').should('be.visible');
    });

    it('TC-7.3.2: Should view available add-ons', () => {
      // Visit the subscription management page
      cy.visit('/settings/subscription');

      // Click on the "Add-ons" tab
      cy.contains('button', 'Add-ons').click();

      // Verify that available add-ons are displayed
      cy.get('[data-testid="addon-additional-users"]').should('be.visible');
      cy.get('[data-testid="addon-advanced-analytics"]').should('be.visible');
      cy.get('[data-testid="addon-priority-support"]').should('be.visible');
    });

    it('TC-7.3.3: Should change subscription plan', () => {
      // Visit the subscription management page
      cy.visit('/settings/subscription');

      // Click on the "Change Plan" button
      cy.contains('button', 'Change Plan').click();

      // Select a different plan
      cy.get('[data-testid="plan-pro"]').click();

      // Click on the "Update Subscription" button
      cy.contains('button', 'Update Subscription').click();

      // Verify subscription update
      cy.contains('Subscription updated successfully').should('be.visible');
      cy.contains('Pro').should('be.visible');
    });

    it('TC-7.3.4: Should add/remove add-ons', () => {
      // Visit the subscription management page
      cy.visit('/settings/subscription');

      // Click on the "Add-ons" tab
      cy.contains('button', 'Add-ons').click();

      // Add an add-on
      cy.get('[data-testid="addon-priority-support"]')
        .contains('button', 'Add')
        .click();

      cy.get('[data-testid="addon-quantity"]').clear().type('1');
      cy.contains('button', 'Add to Subscription').click();

      // Verify add-on addition
      cy.contains('Add-on added successfully').should('be.visible');
      cy.get('[data-testid="active-addons"]')
        .contains('Priority Support')
        .should('be.visible');

      // Remove the add-on
      cy.get('[data-testid="active-addons"]')
        .contains('Priority Support')
        .parent()
        .contains('button', 'Remove')
        .click();

      cy.contains('button', 'Confirm').click();

      // Verify add-on removal
      cy.contains('Add-on removed successfully').should('be.visible');
      cy.get('[data-testid="active-addons"]')
        .contains('Priority Support')
        .should('not.exist');
    });

    it('TC-7.3.5: Should cancel subscription', () => {
      // Visit the subscription management page
      cy.visit('/settings/subscription');

      // Click on the "Manage Subscription" button
      cy.contains('button', 'Manage Subscription').click();

      // Click on the "Cancel Subscription" button
      cy.contains('button', 'Cancel Subscription').click();

      // Confirm cancellation
      cy.contains('button', 'Confirm').click();

      // Verify subscription cancellation
      cy.contains('Subscription canceled successfully').should('be.visible');
      cy.contains('Canceled').should('be.visible');
    });

    it('TC-7.3.6: Should reactivate subscription', () => {
      // Visit the subscription management page
      cy.visit('/settings/subscription');

      // Ensure we have a canceled subscription
      cy.get('body').then(($body) => {
        if ($body.text().includes('Canceled')) {
          // Click on the "Reactivate" button
          cy.contains('button', 'Reactivate').click();

          // Verify subscription reactivation
          cy.contains('Subscription reactivated successfully').should('be.visible');
          cy.contains('Active').should('be.visible');
        } else {
          cy.log('Skipping test: No canceled subscription to reactivate');
        }
      });
    });
  });
});
