/// <reference path="../../support/global.d.ts" />
describe('Subscription Features', () => {
  it('Should navigate to the home page', () => {
    // Visit the home page
    cy.visit('/');

    // Check if the page loads without errors
    cy.get('body').should('exist');
  });

  it('Should check if settings page exists', () => {
    // Visit the settings page
    cy.visit('/settings');

    // Check if the page loads without errors
    cy.get('body').should('exist');

    // Check if the page has a heading
    cy.get('h1, h2, h3').should('exist');
  });

  it('Should check if subscription settings page exists', () => {
    // Visit the subscription settings page
    cy.visit('/settings/subscription');

    // Check if the page loads without errors
    cy.get('body').should('exist');

    // Check if the page has a heading
    cy.get('h1, h2, h3').should('exist');
  });
});
