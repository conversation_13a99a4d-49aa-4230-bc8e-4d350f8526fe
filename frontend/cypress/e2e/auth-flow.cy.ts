/// <reference path="../support/global.d.ts" />

describe('Authentication Flow', () => {
  // This test only checks the redirect behavior
  it('redirects unauthenticated users to login page', () => {
    // Try to access a protected page without authentication
    cy.visit('/settings/subscription', { failOnStatusCode: false });

    // Should be redirected to login page
    cy.url().should('include', '/login');

    // Login page should have the expected elements
    cy.get('input[type="email"]').should('exist');
    cy.get('input[type="password"]').should('exist');
    cy.get('button[type="submit"]').should('exist');
  });
});
