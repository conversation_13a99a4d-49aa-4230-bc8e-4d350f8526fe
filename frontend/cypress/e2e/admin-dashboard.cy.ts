/// <reference path="../support/global.d.ts" />

describe('Admin Dashboard Tests', () => {
  beforeEach(() => {
    // Mock the authentication as a superadmin
    cy.intercept('POST', '**/auth/v1/token?grant_type=password', {
      fixture: 'superadmin-auth.json',
    }).as('loginRequest');

    cy.intercept('GET', '**/auth/v1/user', {
      fixture: 'superadmin-user.json',
    }).as('getUser');

    // Mock the session check
    cy.intercept('GET', '**/auth/v1/session', {
      fixture: 'superadmin-session.json',
    }).as('getSession');

    // Login as superadmin
    cy.visit('/login');
    cy.get('input[name="email"]').type('<EMAIL>');
    cy.get('input[name="password"]').type('password123');
    cy.get('button[type="submit"]').click();
    cy.wait('@loginRequest');
  });

  it('should navigate to the admin dashboard', () => {
    cy.visit('/admin');
    cy.get('h1').should('contain', 'Admin Dashboard');

    // Check that the dashboard cards are displayed
    cy.get('[data-testid="dashboard-card-tenants"]').should('be.visible');
    cy.get('[data-testid="dashboard-card-users"]').should('be.visible');
    cy.get('[data-testid="dashboard-card-subscriptions"]').should('be.visible');
    cy.get('[data-testid="dashboard-card-alerts"]').should('be.visible');
  });

  it('should navigate between dashboard sections', () => {
    cy.visit('/admin');

    // Test navigation to subscription management
    cy.get('a[href="/admin/subscriptions"]').click();
    cy.url().should('include', '/admin/subscriptions');
    cy.get('h1').should('contain', 'Subscription Management');

    // Test navigation to usage monitoring
    cy.get('a[href="/admin/usage"]').click();
    cy.url().should('include', '/admin/usage');
    cy.get('h1').should('contain', 'Usage Monitoring');

    // Test navigation to security dashboard
    cy.get('a[href="/admin/security"]').click();
    cy.url().should('include', '/admin/security');
    cy.get('h1').should('contain', 'Security Dashboard');
  });

  it('should test security dashboard components', () => {
    cy.visit('/admin/security');

    // Test tabs navigation
    cy.get('button').contains('Events').click();
    cy.get('[data-testid="security-events-table"]').should('be.visible');

    cy.get('button').contains('Anomalies').click();
    cy.get('[data-testid="anomalies-dashboard"]').should('be.visible');

    cy.get('button').contains('Alerts').click();
    cy.get('[data-testid="alerts-dashboard"]').should('be.visible');

    cy.get('button').contains('Tokens').click();
    cy.get('[data-testid="tokens-dashboard"]').should('be.visible');
  });

  it('should test security sub-pages', () => {
    // Test security alerts page
    cy.visit('/admin/security/alerts');
    cy.get('h1').should('contain', 'Security Alerts');
    cy.get('[data-testid="alerts-dashboard"]').should('be.visible');

    // Test anomalies page
    cy.visit('/admin/security/anomalies');
    cy.get('h1').should('contain', 'Security Anomalies');
    cy.get('[data-testid="anomalies-dashboard"]').should('be.visible');

    // Test token management page
    cy.visit('/admin/security/tokens');
    cy.get('h1').should('contain', 'Token Management');
    cy.get('[data-testid="tokens-dashboard"]').should('be.visible');
  });

  it('should test subscription management components', () => {
    cy.visit('/admin/subscriptions');

    // Test tabs navigation
    cy.get('button').contains('Tenant Subscriptions').click();
    cy.get('[data-testid="tenant-subscriptions-table"]').should('be.visible');

    cy.get('button').contains('Subscription Plans').click();
    cy.get('[data-testid="subscription-plans-table"]').should('be.visible');

    cy.get('button').contains('Subscription Addons').click();
    cy.get('[data-testid="subscription-addons-table"]').should('be.visible');
  });

  it('should test usage monitoring components', () => {
    cy.visit('/admin/usage');

    // Test tabs navigation
    cy.get('button').contains('Overview').click();
    cy.get('[data-testid="usage-overview-dashboard"]').should('be.visible');

    cy.get('button').contains('AI Tokens').click();
    cy.get('[data-testid="token-usage-dashboard"]').should('be.visible');

    cy.get('button').contains('Voice Agents').click();
    cy.get('[data-testid="voice-agent-usage-dashboard"]').should('be.visible');
  });

  it('should verify authentication protection', () => {
    // Mock an unauthenticated session
    cy.intercept('GET', '**/auth/v1/session', {
      body: { data: { session: null } },
    }).as('getUnauthenticatedSession');

    // Try to access admin dashboard without authentication
    cy.visit('/admin');
    cy.wait('@getUnauthenticatedSession');

    // Should be redirected to login
    cy.url().should('include', '/login');
  });

  it('should verify authorization protection', () => {
    // Mock a regular user session (non-superadmin)
    cy.intercept('GET', '**/auth/v1/session', {
      fixture: 'regular-user-session.json',
    }).as('getRegularUserSession');

    cy.intercept('GET', '**/rest/v1/users*', {
      body: { data: [{ id: 'user-123', role: 'partner' }] },
    }).as('getUserRole');

    // Try to access admin dashboard as a regular user
    cy.visit('/admin');
    cy.wait('@getRegularUserSession');
    cy.wait('@getUserRole');

    // Should be redirected to dashboard
    cy.url().should('include', '/dashboard');
  });
});
