/// <reference path="../support/global.d.ts" />
describe('Subscription Pages', () => {
  it('Should navigate to the home page', () => {
    cy.visit('/');
    cy.get('body').should('exist');
  });

  it('Should navigate to the settings page', () => {
    cy.visit('/settings');
    cy.get('body').should('exist');
  });

  it('Should navigate to the subscription page', () => {
    cy.visit('/settings/subscription');
    cy.get('body').should('exist');
  });

  it('Should navigate to the usage dashboard page', () => {
    cy.visit('/settings/usage');
    cy.get('body').should('exist');
  });
});
