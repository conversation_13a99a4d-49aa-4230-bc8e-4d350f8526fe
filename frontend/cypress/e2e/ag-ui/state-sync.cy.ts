/**
 * AG-UI State Synchronization Tests
 * 
 * Tests the state synchronization between client and server in AG-UI.
 * Verifies that conversation state, message history, and context are properly maintained.
 */

/// <reference types="cypress" />

/// <reference path="../../support/global.d.ts" />

describe('AG-UI State Synchronization', () => {
  beforeEach(() => {
    // Login before each test
    cy.login();
    
    // Enable AG-UI for testing
    cy.window().then((win) => {
      win.localStorage.setItem('NEXT_PUBLIC_AGUI_ENABLED', 'true');
    });

    // Intercept API calls to the copilotkit endpoint
    cy.intercept('POST', '/api/copilotkit', (req) => {
      const body = req.body;
      
      // Check for previous messages in the context
      const hasContext = body && body.messages && body.messages.length > 1;
      
      // Return appropriate response based on the presence of context
      if (hasContext) {
        // Find the most recent user message
        const userMessages = body.messages.filter((m: any) => m.role === 'user');
        const lastUserMessage = userMessages.length ? userMessages[userMessages.length - 1].content : '';
        
        req.reply({
          id: 'state-sync-response',
          object: 'chat.completion',
          created: Date.now(),
          model: 'gpt-4',
          choices: [
            {
              index: 0,
              message: {
                role: 'assistant',
                content: `I see our conversation has context. You just said: "${lastUserMessage}". I can see we have ${body.messages.length} total messages in our history.`,
              },
              finish_reason: 'stop',
            },
          ],
          usage: {
            prompt_tokens: 50 * body.messages.length, // Simulate larger token usage with more history
            completion_tokens: 30,
            total_tokens: 50 * body.messages.length + 30,
          },
        });
      } else {
        req.reply({
          id: 'state-sync-new-response',
          object: 'chat.completion',
          created: Date.now(),
          model: 'gpt-4',
          choices: [
            {
              index: 0,
              message: {
                role: 'assistant',
                content: 'Hello! This appears to be a new conversation. How can I help you today?',
              },
              finish_reason: 'stop',
            },
          ],
          usage: {
            prompt_tokens: 20,
            completion_tokens: 30,
            total_tokens: 50,
          },
        });
      }
    }).as('chatAPI');
  });

  it('should maintain conversation context across messages', () => {
    // Visit the page with a chat component
    cy.visit('/dashboard');
    
    // Wait for the chat component to load
    cy.get('[data-testid="chat-container"]').should('be.visible');
    
    // Send the first message
    cy.get('[data-testid="chat-input"]').type('Hello, my name is John{enter}');
    cy.wait('@chatAPI');
    
    // Send a second message that refers to the first
    cy.get('[data-testid="chat-input"]').type('What did I just tell you my name was?{enter}');
    cy.wait('@chatAPI');
    
    // Verify the response includes the context from the first message
    cy.get('[data-testid="assistant-message"]')
      .last()
      .should('contain', 'John');
  });

  it('should persist state across page refreshes', () => {
    // Visit the page with a chat component
    cy.visit('/dashboard');
    
    // Wait for the chat component to load
    cy.get('[data-testid="chat-container"]').should('be.visible');
    
    // Send a message to establish some state
    cy.get('[data-testid="chat-input"]').type('Remember that my case number is ABC-12345{enter}');
    cy.wait('@chatAPI');
    
    // Refresh the page
    cy.reload();
    
    // Wait for the chat component to reload
    cy.get('[data-testid="chat-container"]').should('be.visible');
    
    // Verify message history is still present after refresh
    cy.get('[data-testid="user-message"]')
      .should('contain', 'Remember that my case number is ABC-12345');
    
    // Send a follow-up message referring to the previous state
    cy.get('[data-testid="chat-input"]').type('What was my case number again?{enter}');
    cy.wait('@chatAPI');
    
    // Verify the response includes the context from before the refresh
    cy.get('[data-testid="assistant-message"]')
      .last()
      .should('contain', 'ABC-12345');
  });

  it('should synchronize state across different chat components', () => {
    // First use the dashboard chat
    cy.visit('/dashboard');
    cy.get('[data-testid="chat-container"]').should('be.visible');
    cy.get('[data-testid="chat-input"]').type('I need help with a car accident case from May 15, 2022{enter}');
    cy.wait('@chatAPI');
    
    // Now navigate to the research page
    cy.visit('/research');
    cy.get('[data-testid="chat-container"]').should('be.visible');
    
    // Check if context carries over (if designed to share context)
    cy.get('[data-testid="chat-input"]').type('When did I say my accident occurred?{enter}');
    cy.wait('@chatAPI');
    
    // This test would pass or fail depending on whether the system is designed to share context
    // If shared context is expected:
    cy.get('[data-testid="assistant-message"]')
      .last()
      .should('contain', 'May 15, 2022');
    
    // If separate context is expected, check for the absence instead:
    // cy.get('[data-testid="assistant-message"]')
    //   .last()
    //   .should('not.contain', 'May 15, 2022');
  });

  it('should maintain user identity and permissions context', () => {
    // Set up mock user data
    const mockUser = {
      id: 'user-123',
      name: 'John Smith',
      email: '<EMAIL>',
      role: 'attorney',
      permissions: ['read_cases', 'edit_cases', 'view_sensitive_info']
    };
    
    // Force login as this specific user
    cy.window().then((win) => {
      // Store user data in localStorage
      win.localStorage.setItem('user_data', JSON.stringify(mockUser));
    });
    
    // Set up an intercept that validates user context is passed
    cy.intercept('POST', '/api/copilotkit', (req) => {
      // Check if user context headers are present
      const hasUserContext = req.headers && req.headers.authorization;
      
      if (hasUserContext) {
        req.reply({
          id: 'user-context-response',
          choices: [{
            message: {
              role: 'assistant',
              content: `Your identity as ${mockUser.name} (${mockUser.role}) with appropriate permissions is confirmed in the system.`,
            },
            finish_reason: 'stop'
          }]
        });
      } else {
        req.reply({
          id: 'missing-context-response',
          choices: [{
            message: {
              role: 'assistant',
              content: `I cannot verify your identity or permissions.`,
            },
            finish_reason: 'stop'
          }]
        });
      }
    }).as('userContextAPI');
    
    // Visit the page and check user context
    cy.visit('/dashboard');
    cy.get('[data-testid="chat-container"]').should('be.visible');
    cy.get('[data-testid="chat-input"]').type('Verify my identity and permissions{enter}');
    cy.wait('@userContextAPI');
    
    // Verify the response includes the user context
    cy.get('[data-testid="assistant-message"]')
      .last()
      .should('contain', 'John Smith')
      .should('contain', 'attorney');
  });

  it('should properly handle multi-tenant isolation', () => {
    // Set up different organizational contexts
    const org1Context = {
      organization_id: 'org-123',
      organization_name: 'Law Firm A',
      tenant_id: 'tenant-a'
    };
    
    const org2Context = {
      organization_id: 'org-456',
      organization_name: 'Law Firm B',
      tenant_id: 'tenant-b'
    };
    
    // Set up intercepts for different tenant contexts
    cy.intercept('POST', '/api/copilotkit', (req) => {
      // Extract the tenant information from the request
      const authHeader = req.headers.authorization || '';
      const isTenantA = authHeader.includes('tenant-a');
      const isTenantB = authHeader.includes('tenant-b');
      
      if (isTenantA) {
        req.reply({
          id: 'tenant-a-response',
          choices: [{
            message: {
              role: 'assistant',
              content: 'Your data is isolated to Law Firm A context.',
            },
            finish_reason: 'stop'
          }]
        });
      } else if (isTenantB) {
        req.reply({
          id: 'tenant-b-response',
          choices: [{
            message: {
              role: 'assistant',
              content: 'Your data is isolated to Law Firm B context.',
            },
            finish_reason: 'stop'
          }]
        });
      } else {
        req.reply({
          id: 'unknown-tenant-response',
          choices: [{
            message: {
              role: 'assistant',
              content: 'Unable to determine your organization context.',
            },
            finish_reason: 'stop'
          }]
        });
      }
    }).as('tenantAPI');
    
    // Test with the first tenant
    cy.window().then((win) => {
      // Store org context in localStorage
      win.localStorage.setItem('org_context', JSON.stringify(org1Context));
    });
    
    cy.visit('/dashboard');
    cy.get('[data-testid="chat-container"]').should('be.visible');
    cy.get('[data-testid="chat-input"]').type('What organization am I in?{enter}');
    cy.wait('@tenantAPI');
    
    // Verify the response is tenant A specific
    cy.get('[data-testid="assistant-message"]')
      .last()
      .should('contain', 'Law Firm A');
    
    // Clear the chat
    cy.get('[data-testid="clear-chat-button"]').click();
    
    // Switch to the second tenant
    cy.window().then((win) => {
      win.localStorage.setItem('org_context', JSON.stringify(org2Context));
    });
    
    // Reload to apply the new context
    cy.reload();
    cy.get('[data-testid="chat-container"]').should('be.visible');
    cy.get('[data-testid="chat-input"]').type('What organization am I in?{enter}');
    cy.wait('@tenantAPI');
    
    // Verify the response is tenant B specific
    cy.get('[data-testid="assistant-message"]')
      .last()
      .should('contain', 'Law Firm B');
  });
});
