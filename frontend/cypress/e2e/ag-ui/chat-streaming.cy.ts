/**
 * AG-UI Chat Streaming Tests
 * 
 * Tests the streaming functionality of the AG-UI protocol in chat components.
 * Verifies proper message rendering, streaming behavior, and error handling.
 */

/// <reference types="cypress" />

/// <reference path="../../support/global.d.ts" />

describe('AG-UI Chat Streaming', () => {
  beforeEach(() => {
    // Login before each test
    cy.login();
    
    // Enable AG-UI for testing
    cy.window().then((win) => {
      win.localStorage.setItem('NEXT_PUBLIC_AGUI_ENABLED', 'true');
    });

    // Set up interceptors for the AG-UI endpoints
    cy.intercept('POST', '/api/copilotkit', (req) => {
      // Return a mock response for non-streaming chat
      req.reply({
        body: {
          id: 'mock-chat-id',
          object: 'chat.completion',
          created: Date.now(),
          model: 'gpt-4',
          choices: [
            {
              index: 0,
              message: {
                role: 'assistant',
                content: 'This is a mock response from the AG-UI API.',
              },
              finish_reason: 'stop',
            },
          ],
          usage: {
            prompt_tokens: 50,
            completion_tokens: 30,
            total_tokens: 80,
          },
        },
      });
    }).as('chatCompletionAPI');

    // Intercept SSE streaming endpoint
    cy.intercept('GET', '/api/copilotkit*', (req) => {
      // For streaming, we need to respond with a proper SSE stream
      req.reply((res) => {
        res.send({
          statusCode: 200,
          headers: {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
          },
          body: [
            'data: {"id":"mock-stream-1","object":"chat.completion.chunk","created":' + Date.now() + ',"model":"gpt-4","choices":[{"index":0,"delta":{"role":"assistant","content":"This "},"finish_reason":null}]}\n\n',
            'data: {"id":"mock-stream-2","object":"chat.completion.chunk","created":' + Date.now() + ',"model":"gpt-4","choices":[{"index":0,"delta":{"content":"is "},"finish_reason":null}]}\n\n',
            'data: {"id":"mock-stream-3","object":"chat.completion.chunk","created":' + Date.now() + ',"model":"gpt-4","choices":[{"index":0,"delta":{"content":"a "},"finish_reason":null}]}\n\n',
            'data: {"id":"mock-stream-4","object":"chat.completion.chunk","created":' + Date.now() + ',"model":"gpt-4","choices":[{"index":0,"delta":{"content":"streamed "},"finish_reason":null}]}\n\n',
            'data: {"id":"mock-stream-5","object":"chat.completion.chunk","created":' + Date.now() + ',"model":"gpt-4","choices":[{"index":0,"delta":{"content":"response."},"finish_reason":null}]}\n\n',
            'data: {"id":"mock-stream-6","object":"chat.completion.chunk","created":' + Date.now() + ',"model":"gpt-4","choices":[{"index":0,"delta":{},"finish_reason":"stop"}]}\n\n',
            'data: [DONE]\n\n',
          ].join(''),
        });
      });
    }).as('chatStreamingAPI');
  });

  it('should render the chat interface correctly', () => {
    // Visit the page with a chat component
    cy.visit('/dashboard');
    
    // Wait for the chat component to load
    cy.get('[data-testid="chat-container"]', { timeout: 10000 }).should('be.visible');
    
    // Check that the chat title is rendered
    cy.get('[data-testid="chat-title"]').should('be.visible');
    
    // Check that the input area is rendered
    cy.get('[data-testid="chat-input"]').should('be.visible');
  });

  it('should send a message and receive a streamed response', () => {
    // Visit the page with a chat component
    cy.visit('/dashboard');
    
    // Wait for the chat component to load
    cy.get('[data-testid="chat-container"]').should('be.visible');
    
    // Type a message
    cy.get('[data-testid="chat-input"]').type('Hello, I need some legal advice{enter}');
    
    // Verify user message appears
    cy.get('[data-testid="user-message"]').last().should('contain', 'Hello, I need some legal advice');
    
    // Wait for the streaming response to start
    cy.wait('@chatStreamingAPI');
    
    // Check that a loading/streaming indicator is shown
    cy.get('[data-testid="assistant-message-loading"]').should('be.visible');
    
    // Check the final streamed message
    cy.get('[data-testid="assistant-message"]', { timeout: 10000 })
      .last()
      .should('contain', 'This is a streamed response.');
  });

  it('should handle message history correctly', () => {
    // Visit the page with a chat component
    cy.visit('/dashboard');
    
    // Wait for the chat component to load
    cy.get('[data-testid="chat-container"]').should('be.visible');
    
    // Send first message
    cy.get('[data-testid="chat-input"]').type('First message{enter}');
    cy.wait('@chatStreamingAPI');
    
    // Send second message
    cy.get('[data-testid="chat-input"]').type('Second message{enter}');
    cy.wait('@chatStreamingAPI');
    
    // Verify both messages and responses are present
    cy.get('[data-testid="user-message"]').should('have.length', 2);
    cy.get('[data-testid="assistant-message"]').should('have.length', 2);
    
    // Refresh the page
    cy.reload();
    
    // Verify message history is still present (if persistence is implemented)
    cy.get('[data-testid="user-message"]').should('have.length.at.least', 2);
    cy.get('[data-testid="assistant-message"]').should('have.length.at.least', 2);
  });

  it('should handle network errors gracefully', () => {
    // Visit the page with a chat component
    cy.visit('/dashboard');
    
    // Wait for the chat component to load
    cy.get('[data-testid="chat-container"]').should('be.visible');
    
    // Set up a network error
    cy.intercept('GET', '/api/copilotkit*', {
      statusCode: 500,
      body: 'Internal Server Error',
    }).as('networkErrorAPI');
    
    // Type a message
    cy.get('[data-testid="chat-input"]').type('This should trigger an error{enter}');
    
    // Wait for the error to be triggered
    cy.wait('@networkErrorAPI');
    
    // Check that the error message is displayed
    cy.get('[data-testid="error-message"]', { timeout: 10000 })
      .should('be.visible')
      .should('contain', 'Error');
    
    // Check for retry button
    cy.get('[data-testid="retry-button"]').should('be.visible');
  });

  it('should test guardrails for content filtering', () => {
    // Visit the page with a chat component
    cy.visit('/dashboard');
    
    // Wait for the chat component to load
    cy.get('[data-testid="chat-container"]').should('be.visible');
    
    // Set up the guardrail intercept
    cy.intercept('POST', '/api/copilotkit', {
      statusCode: 403,
      body: {
        error: 'Content policy violation',
        type: 'harmful',
        message: 'Your request contains potentially harmful content. For everyone\'s safety, please rephrase your request.'
      }
    }).as('guardrailAPI');
    
    // Type a message that should trigger guardrails
    cy.get('[data-testid="chat-input"]').type('Tell me how to hack into a system{enter}');
    
    // Wait for the guardrail to be triggered
    cy.wait('@guardrailAPI');
    
    // Check that the policy violation message is displayed
    cy.get('[data-testid="guardrail-message"]', { timeout: 10000 })
      .should('be.visible')
      .should('contain', 'content policy');
  });
});
