/// <reference path="../../support/global.d.ts" />

describe('Quota Limits', () => {
  context('Approaching Quota Limits', () => {
    beforeEach(() => {
      // Login as the main test user (which has approaching quota limits)
      cy.login(Cypress.env('TEST_USER_EMAIL'), "password");

      // Wait for authentication to complete
      cy.wait(1000);
    });

    it('Should display quota warning on usage dashboard', () => {
      // Visit the usage dashboard page
      cy.visit('/settings/usage', {
        failOnStatusCode: false,
        timeout: 10000
      });

      // Force the test to wait for the page to load
      cy.wait(2000);

      // Look for quota warning content
      cy.get('body', { timeout: 10000 }).contains(/approaching|warning|limit|quota/i).should('exist');

      // Check for the yellow warning indicator
      cy.get('[class*="bg-yellow"]', { timeout: 10000 }).should('exist');

      // Check for quota warning indicators
      cy.get('body').then(($body) => {
        const bodyText = $body.text().toLowerCase();
        const hasWarningIndicators =
          bodyText.includes('approaching') ||
          bodyText.includes('warning') ||
          bodyText.includes('limit') ||
          bodyText.includes('quota');

        expect(hasWarningIndicators).to.be.true;
      });
    });
  });

  context('Exceeded Quota Limits', () => {
    beforeEach(() => {
      // Login as a user with exceeded quota limits
      cy.login(Cypress.env('TEST_ACTIVE_USER_EMAIL'), "password"); // Active user has exceeded quota in our mock

      // Wait for authentication to complete
      cy.wait(1000);
    });

    it('Should display quota exceeded warning on usage dashboard', () => {
      // Visit the usage dashboard page
      cy.visit('/settings/usage', {
        failOnStatusCode: false,
        timeout: 10000
      });

      // Force the test to wait for the page to load
      cy.wait(2000);

      // Look for quota exceeded content
      cy.get('body', { timeout: 10000 }).contains(/exceeded|limit|quota|upgrade/i).should('exist');

      // Check for the red warning indicator
      cy.get('[class*="bg-red"]', { timeout: 10000 }).should('exist');

      // Check for quota exceeded indicators
      cy.get('body').then(($body) => {
        const bodyText = $body.text().toLowerCase();
        const hasExceededIndicators =
          bodyText.includes('exceeded') ||
          bodyText.includes('limit') ||
          bodyText.includes('quota') ||
          bodyText.includes('upgrade');

        expect(hasExceededIndicators).to.be.true;
      });
    });
  });
});
