/// <reference types="cypress" />
/// <reference path="./global.d.ts" />

declare namespace Cypress {
  interface Chainable<Subject = any> {
    /**
     * Custom command to log in with email and password
     * @example cy.login('<EMAIL>', 'password')
     */
    login(email?: string, password?: string): Chainable<void>;

    /**
     * Custom command to navigate to a specific page while authenticated
     * @example cy.navigateAuthenticated('/dashboard')
     */
    navigateAuthenticated(url: string): Chainable<Element>;

    /**
     * Custom command to check if user is authenticated
     * @example cy.checkAuthentication()
     */
    checkAuthentication(): Chainable<Element>;

    /**
     * Custom command to select an option from Shadcn select component
     * @example cy.shadcnSelect('[data-testid="my-select"]', 'Option Text')
     */
    shadcnSelect(selector: string, optionText: string): Chainable<Element>;

    /**
     * Custom command to interact with AI input without waiting for real AI response
     * @example cy.mockAIInteraction('[data-testid="ai-input"]', 'Hello AI', 'The AI response text')
     */
    mockAIInteraction(selector: string, input: string, mockResponse: string): Chainable<Element>;

    /**
     * Custom command to log out a user
     * @example cy.logout()
     */
    logout(): Chainable<void>;

    /**
     * Custom command to attach a file to an input
     * @example cy.attachFile('example.pdf')
     */
    attachFile(filePath: string): Chainable<void>;

    /**
     * Custom command to mount a React component
     * @example cy.mount(<MyComponent />)
     */
    mount: typeof mount;
  }
}

// Import mount from cypress/react18 to make it available in the global namespace
import { mount } from 'cypress/react18';
